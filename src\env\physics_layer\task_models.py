"""
Task-related data models for SPACE2 simulation
Defines all data structures used in task generation and distribution
"""

from dataclasses import dataclass, field
from typing import Tuple, Optional, List, Dict, Any
from enum import Enum


class TaskType(Enum):
    """Task type enumeration"""
    REALTIME = 1          # Real-time task with low latency requirement
    NORMAL = 2            # Normal task with moderate requirements
    COMPUTE_INTENSIVE = 3 # Compute-intensive task with high complexity


class GeographyType(Enum):
    """Geography type enumeration"""
    LAND = "Land"
    OCEAN = "Ocean"


class ScaleType(Enum):
    """Location scale enumeration"""
    SMALL = "Small"
    MEDIUM = "Medium" 
    LARGE = "Large"


class FunctionalType(Enum):
    """Functional type enumeration"""
    NORMAL = "Normal"
    INDUSTRIAL = "Industrial"
    DELAY_SENSITIVE = "DelaySensitive"


class AssignmentStatus(Enum):
    """Task assignment status"""
    PENDING = "pending"           # Waiting for assignment
    ASSIGNED = "assigned"         # Successfully assigned to satellite
    RETRYING = "retrying"        # Retrying after failure
    FAILED = "failed"            # Failed after max retries
    NO_SATELLITE = "no_satellite" # No visible satellite


@dataclass
class Location:
    """Geographic location node"""
    location_id: int
    latitude: float
    longitude: float
    geography: GeographyType
    scale: ScaleType
    functional_type: FunctionalType
    
    @property
    def coordinates(self) -> Tuple[float, float]:
        """Get coordinates as tuple"""
        return (self.latitude, self.longitude)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "location_id": self.location_id,
            "latitude": self.latitude,
            "longitude": self.longitude,
            "geography": self.geography.value,
            "scale": self.scale.value,
            "functional_type": self.functional_type.value
        }


@dataclass
class Task:
    """Task data structure"""
    task_id: int
    type_id: TaskType
    data_size_mb: float
    complexity_cycles_per_bit: int
    deadline_timestamp: float
    priority: int
    location_id: int
    coordinates: Tuple[float, float]
    generation_time: float
    
    # Optional fields for tracking
    geography: Optional[GeographyType] = None
    scale: Optional[ScaleType] = None
    functional_type: Optional[FunctionalType] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        result = {
            "task_id": self.task_id,
            "type_id": self.type_id.value if isinstance(self.type_id, TaskType) else self.type_id,
            "data_size_mb": self.data_size_mb,
            "complexity_cycles_per_bit": self.complexity_cycles_per_bit,
            "deadline_timestamp": self.deadline_timestamp,
            "priority": self.priority,
            "location_id": self.location_id,
            "coordinates": list(self.coordinates),
            "generation_time": self.generation_time
        }
        
        # Add optional fields if present
        if self.geography:
            result["geography"] = self.geography.value if isinstance(self.geography, GeographyType) else self.geography
        if self.scale:
            result["scale"] = self.scale.value if isinstance(self.scale, ScaleType) else self.scale
        if self.functional_type:
            result["functional_type"] = self.functional_type.value if isinstance(self.functional_type, FunctionalType) else self.functional_type
            
        return result
    
    @property
    def total_complexity(self) -> float:
        """Calculate total computational complexity in CPU cycles"""
        return self.complexity_cycles_per_bit * self.data_size_mb * 8 * 1024 * 1024


@dataclass
class TaskAssignment:
    """Task assignment record"""
    task_id: int
    task: Task
    assigned_satellite_id: Optional[int] = None
    assignment_time: Optional[float] = None
    distance_km: Optional[float] = None
    retry_count: int = 0
    status: AssignmentStatus = AssignmentStatus.PENDING
    failure_reason: Optional[str] = None
    
    # Retransmission tracking
    retransmission_times: List[float] = field(default_factory=list)
    last_attempt_time: Optional[float] = None
    
    def mark_assigned(self, satellite_id: int, distance: float, time: float):
        """Mark task as successfully assigned"""
        self.assigned_satellite_id = satellite_id
        self.distance_km = distance
        self.assignment_time = time
        self.status = AssignmentStatus.ASSIGNED
        self.last_attempt_time = time
    
    def mark_retry(self, time: float):
        """Mark task for retry"""
        self.retry_count += 1
        self.status = AssignmentStatus.RETRYING
        self.retransmission_times.append(time)
        self.last_attempt_time = time
    
    def mark_failed(self, reason: str):
        """Mark task as failed"""
        self.status = AssignmentStatus.FAILED
        self.failure_reason = reason
    
    def can_retry(self, max_retries: int = 2) -> bool:
        """Check if task can be retried"""
        return self.retry_count < max_retries and self.status != AssignmentStatus.FAILED
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "task_id": self.task_id,
            "task": self.task.to_dict(),
            "assigned_satellite_id": self.assigned_satellite_id,
            "assignment_time": self.assignment_time,
            "distance_km": self.distance_km,
            "retry_count": self.retry_count,
            "status": self.status.value,
            "failure_reason": self.failure_reason,
            "retransmission_times": self.retransmission_times,
            "last_attempt_time": self.last_attempt_time
        }


@dataclass  
class TaskGenerationConfig:
    """Configuration for task generation"""
    # Lambda parameters for different geography types
    ocean_lambda: float = 1.0
    land_large_lambda: float = 3.0
    land_medium_lambda: float = 2.0
    land_small_lambda: float = 1.0
    
    # Task type probabilities for different functional types
    normal_probabilities: List[float] = field(default_factory=lambda: [0.2, 0.6, 0.2])
    industrial_probabilities: List[float] = field(default_factory=lambda: [0.2, 0.2, 0.6])
    delay_sensitive_probabilities: List[float] = field(default_factory=lambda: [0.6, 0.2, 0.2])
    
    # Task parameters ranges [min, max] for each task type
    type1_data_range: Tuple[float, float] = (10.0, 20.0)
    type1_complexity: int = 100
    type1_deadline_offset: float = 5.0
    
    type2_data_range: Tuple[float, float] = (20.0, 30.0)
    type2_complexity: int = 150
    type2_deadline_offset: float = 15.0
    
    type3_data_range: Tuple[float, float] = (30.0, 50.0)
    type3_complexity: int = 200
    type3_deadline_offset: float = 30.0
    
    # Priority range
    min_priority: int = 1
    max_priority: int = 3
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'TaskGenerationConfig':
        """Create from config dictionary"""
        # Extract task generation specific config if it exists
        task_config = config.get('task_generation', {})
        
        return cls(
            ocean_lambda=task_config.get('ocean_lambda', 1.0),
            land_large_lambda=task_config.get('land_large_lambda', 10.0),
            land_medium_lambda=task_config.get('land_medium_lambda', 6.0),
            land_small_lambda=task_config.get('land_small_lambda', 3.0),
            normal_probabilities=task_config.get('normal_probabilities', [0.2, 0.6, 0.2]),
            industrial_probabilities=task_config.get('industrial_probabilities', [0.2, 0.2, 0.6]),
            delay_sensitive_probabilities=task_config.get('delay_sensitive_probabilities', [0.6, 0.2, 0.2]),
            type1_data_range=tuple(task_config.get('type1_data_range', [10.0, 20.0])),
            type1_complexity=task_config.get('type1_complexity', 100),
            type1_deadline_offset=task_config.get('type1_deadline_offset', 5.0),
            type2_data_range=tuple(task_config.get('type2_data_range', [20.0, 50.0])),
            type2_complexity=task_config.get('type2_complexity', 200),
            type2_deadline_offset=task_config.get('type2_deadline_offset', 15.0),
            type3_data_range=tuple(task_config.get('type3_data_range', [50.0, 150.0])),
            type3_complexity=task_config.get('type3_complexity', 300),
            type3_deadline_offset=task_config.get('type3_deadline_offset', 30.0),
            min_priority=task_config.get('min_priority', 1),
            max_priority=task_config.get('max_priority', 3)
        )


@dataclass
class DistributionMetrics:
    """Metrics for task distribution"""
    total_tasks: int = 0
    assigned_tasks: int = 0
    failed_tasks: int = 0
    retried_tasks: int = 0
    no_satellite_tasks: int = 0
    
    # Performance metrics
    avg_assignment_distance: float = 0.0
    avg_retry_count: float = 0.0
    assignment_success_rate: float = 0.0
    
    # Time-based metrics
    tasks_per_timeslot: Dict[int, int] = field(default_factory=dict)
    assignments_per_satellite: Dict[int, int] = field(default_factory=dict)
    
    def update_assignment(self, assignment: TaskAssignment):
        """Update metrics based on assignment"""
        self.total_tasks += 1
        
        if assignment.status == AssignmentStatus.ASSIGNED:
            self.assigned_tasks += 1
            if assignment.assigned_satellite_id:
                self.assignments_per_satellite[assignment.assigned_satellite_id] = \
                    self.assignments_per_satellite.get(assignment.assigned_satellite_id, 0) + 1
        elif assignment.status == AssignmentStatus.FAILED:
            self.failed_tasks += 1
        elif assignment.status == AssignmentStatus.NO_SATELLITE:
            self.no_satellite_tasks += 1
            
        if assignment.retry_count > 0:
            self.retried_tasks += 1
    
    def calculate_statistics(self):
        """Calculate derived statistics"""
        if self.total_tasks > 0:
            self.assignment_success_rate = self.assigned_tasks / self.total_tasks
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "total_tasks": self.total_tasks,
            "assigned_tasks": self.assigned_tasks,
            "failed_tasks": self.failed_tasks,
            "retried_tasks": self.retried_tasks,
            "no_satellite_tasks": self.no_satellite_tasks,
            "avg_assignment_distance": self.avg_assignment_distance,
            "avg_retry_count": self.avg_retry_count,
            "assignment_success_rate": self.assignment_success_rate,
            "tasks_per_timeslot": self.tasks_per_timeslot,
            "assignments_per_satellite": self.assignments_per_satellite
        }