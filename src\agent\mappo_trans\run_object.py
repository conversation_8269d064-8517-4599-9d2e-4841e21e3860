"""
Application entry point for MAPPO on SPACE3
应用入口 - 模仿参考代码的run_object.py结构
按时隙输出结果，替代测试脚本
"""

import os
import sys
from typing import List, Dict

# 添加项目路径
curr_path = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(curr_path)))
sys.path.append(project_root)

from src.agent.mappo_trans.run_ppo import run_ppo
from src.agent.mappo_trans.utils import plot_rewards, save_timeslot_results
from src.agent.mappo_trans.config import get_default_config


def save_results_to_files(rewards: List[float], completion_rate: float, 
                         avg_time: float, utilization: float, config):
    """
    保存结果到文件
    模仿参考代码的结果保存方式
    
    Args:
        rewards: 训练奖励列表
        completion_rate: 完成率
        avg_time: 平均时间
        utilization: 利用率
        config: 配置参数
    """
    # 确保数据目录存在
    data_dir = os.path.join(curr_path, "data")
    os.makedirs(data_dir, exist_ok=True)
    
    # 保存reward曲线
    reward_file = os.path.join(data_dir, "reward.txt")
    with open(reward_file, "a") as file:
        for reward in rewards:
            file.write(str(reward) + "\n")
    print(f"Rewards saved to {reward_file}")
    
    # 保存完成率曲线
    completion_file = os.path.join(data_dir, "completion_rate.txt")
    with open(completion_file, "a") as file:
        file.write(str(completion_rate) + "\n")
    print(f"Completion rate saved to {completion_file}")
    
    # 保存时间曲线
    time_file = os.path.join(data_dir, "time.txt")
    with open(time_file, "a") as file:
        file.write(str(avg_time) + "\n")
    print(f"Time saved to {time_file}")
    
    # 保存利用率曲线
    utilization_file = os.path.join(data_dir, "utilization.txt")
    with open(utilization_file, "a") as file:
        file.write(str(utilization) + "\n")
    print(f"Utilization saved to {utilization_file}")


def plot_training_results(rewards: List[float], config):
    """
    绘制训练结果
    模仿参考代码的可视化
    
    Args:
        rewards: 训练奖励
        config: 配置参数
    """
    if len(rewards) == 0:
        print("No rewards to plot")
        return
    
    try:
        # 绘制奖励曲线
        plot_rewards(rewards, config, tag="train")
        print("Training curves plotted successfully")
        
        # 可以添加更多可视化
        # plot_completion_rate(completion_rates, config, tag="train")
        # plot_utilization(utilizations, config, tag="train")
        
    except Exception as e:
        print(f"Error plotting results: {e}")


def output_final_summary(rewards: List[float], completion_rate: float, 
                        avg_time: float, utilization: float, config):
    """
    输出最终训练总结
    
    Args:
        rewards: 训练奖励
        completion_rate: 完成率
        avg_time: 平均时间
        utilization: 利用率
        config: 配置参数
    """
    print("\n" + "="*60)
    print("MAPPO SPACE3 训练总结")
    print("="*60)
    
    if rewards:
        print(f"训练轮次: {len(rewards)}")
        print(f"最终奖励: {rewards[-1]:.3f}")
        print(f"最高奖励: {max(rewards):.3f}")
        print(f"平均奖励: {sum(rewards)/len(rewards):.3f}")
        print(f"奖励标准差: {(sum([(r-sum(rewards)/len(rewards))**2 for r in rewards])/len(rewards))**0.5:.3f}")
    else:
        print("训练轮次: 0")
        print("无有效奖励数据")
    
    print(f"任务完成率: {completion_rate:.2%}")
    print(f"平均处理时间: {avg_time:.2f}s")
    print(f"系统利用率: {utilization:.2%}")
    
    print(f"\n算法配置:")
    print(f"  - 算法: {config.algo_name}")
    print(f"  - 智能体数量: {config.n_agents}")
    print(f"  - 学习率: {config.lr_a}")
    print(f"  - 批次大小: {config.batch_size}")
    print(f"  - 总训练步数: {config.total_timesteps}")
    
    print("="*60)


def main():
    """
    主函数 - 应用程序入口
    模仿参考代码的main函数结构
    """
    print("启动 MAPPO Transformer on SPACE3 训练...")
    print(f"工作目录: {curr_path}")
    
    try:
        # 运行PPO训练（这里会按时隙输出）
        rewards, completion_rate, avg_time, utilization, config = run_ppo()
        
        print("\n训练完成，处理结果...")
        
        # 保存结果到文件
        save_results_to_files(rewards, completion_rate, avg_time, utilization, config)
        
        # 绘制训练结果
        plot_training_results(rewards, config)
        
        # 输出最终总结
        output_final_summary(rewards, completion_rate, avg_time, utilization, config)
        
        print(f"\n所有结果已保存到: {os.path.join(curr_path, 'data')}")
        print(f"模型已保存到: {config.model_dir}")
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"\n训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n程序结束")


# 简化的时隙输出函数（在训练过程中调用）
def output_timeslot_info(timeslot: int, metrics: Dict):
    """
    按时隙输出信息
    这个函数在run_ppo.py中的log_metrics中被调用
    
    Args:
        timeslot: 当前时隙
        metrics: 指标字典
    """
    print(f"[时隙 {timeslot:4d}] "
          f"奖励: {metrics.get('Reward', 0):7.2f} | "
          f"完成: {metrics.get('Complete', 0):3d} | "
          f"丢弃: {metrics.get('Drop', 0):3d} | "
          f"完成率: {metrics.get('Rate', 0):.2%}")


if __name__ == "__main__":
    # 直接运行主程序
    main()