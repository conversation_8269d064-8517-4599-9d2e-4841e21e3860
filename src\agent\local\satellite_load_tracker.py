"""
卫星负载跟踪器
跟踪指定卫星在仿真过程中的负载变化情况
"""

import numpy as np
import pandas as pd
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Absolute imports as per coding standard
from src.env.Foundation_Layer.time_manager import create_time_manager_from_config
from src.env.Foundation_Layer.logging_config import get_logger
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_distributor import TaskDistributor
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.agent.local.local_compute_simulator import LocalComputeSimulator


class SatelliteLoadTracker:
    """跟踪特定卫星的负载变化"""
    
    def __init__(self, config_path: str = None, target_satellites: List[int] = None):
        """
        初始化负载跟踪器
        
        Args:
            config_path: 配置文件路径
            target_satellites: 要跟踪的卫星ID列表
        """
        # 默认跟踪的卫星
        if target_satellites is None:
            target_satellites = [111, 112, 121, 131, 181]
        
        self.target_satellites = target_satellites
        self.config_path = config_path
        
        # 不在这里初始化仿真器，而是在track_loads中每次创建新实例
        self.simulator = None
        
        # 负载历史记录
        self.load_history = {sat_id: [] for sat_id in target_satellites}
        
        # 统计数据
        self.statistics = {sat_id: {} for sat_id in target_satellites}
        
        self.logger = get_logger(__name__)
        
    def track_loads(self, total_timeslots: int = 2000) -> Dict[int, List[Dict]]:
        """
        跟踪卫星负载
        
        Args:
            total_timeslots: 总时隙数
            
        Returns:
            每个卫星的负载历史
        """
        self.logger.info(f"开始跟踪 {len(self.target_satellites)} 个卫星的负载，共 {total_timeslots} 个时隙")
        
        # 重置历史记录
        self.load_history = {sat_id: [] for sat_id in self.target_satellites}
        
        # 每次创建全新的仿真器实例，确保状态完全干净
        self.simulator = LocalComputeSimulator(config_path=self.config_path)
        
        # 初始化组件
        self.simulator._initialize_components()
        
        # 确保任务生成器从0开始
        self.simulator.task_generator.task_id_counter = 0
        self.logger.info(f"任务ID计数器已重置为: {self.simulator.task_generator.task_id_counter}")
        
        # 重置所有状态
        self.simulator.timeslot_results = []
        self.simulator.all_completed_tasks = []
        self.simulator.all_dropped_tasks = []
        self.simulator.all_tasks_map = {}
        
        # 重置任务统计
        for task_type in self.simulator.task_stats_by_type:
            self.simulator.task_stats_by_type[task_type] = {'generated': 0, 'completed': 0}
        for priority in self.simulator.task_stats_by_priority:
            self.simulator.task_stats_by_priority[priority] = {'generated': 0, 'completed': 0}
        
        # 运行仿真并跟踪
        for timeslot in range(total_timeslots):
            # 每100个时隙输出进度
            if timeslot % 100 == 0:
                self.logger.info(f"Processing timeslot {timeslot}/{total_timeslots}")
            
            # 处理单个时隙
            result = self.simulator._process_single_timeslot(timeslot)
            
            # 将结果添加到仿真器的结果列表（保持一致性）
            if result:
                self.simulator.timeslot_results.append(result)
            
            # 记录目标卫星的负载
            for sat_id in self.target_satellites:
                if sat_id in self.simulator.satellites:
                    satellite = self.simulator.satellites[sat_id]
                    
                    # 获取负载快照
                    load_snapshot = {
                        'timeslot': timeslot,
                        'time_seconds': timeslot * 3,  # 每时隙3秒
                        'queue_length': len(satellite.task_queue),
                        'processing_count': len(satellite.processing_tasks),
                        'total_in_system': len(satellite.task_queue) + len(satellite.processing_tasks),
                        'cpu_utilization': len(satellite.processing_tasks) / 200.0,  # max_parallel_tasks = 200
                        'battery_energy': satellite.battery_energy,
                        'battery_percentage': (satellite.battery_energy / satellite.max_battery) * 100,
                        'illuminated': self.simulator._check_satellite_illumination(sat_id, timeslot),
                        'tasks_completed': satellite.total_tasks_processed,
                        'tasks_dropped': satellite.total_tasks_dropped,
                        'energy_consumed': satellite.total_energy_consumed,
                        'tasks_received': result.tasks_assigned if result and hasattr(result, 'tasks_assigned') else 0
                    }
                    
                    self.load_history[sat_id].append(load_snapshot)
        
        # 输出任务生成统计
        total_generated = sum(r.tasks_generated for r in self.simulator.timeslot_results if r)
        total_completed = len(self.simulator.all_completed_tasks)
        self.logger.info(f"总任务生成数: {total_generated}, 总完成数: {total_completed}")
        
        # 计算统计数据
        self._calculate_statistics()
        
        return self.load_history
    
    def _calculate_statistics(self):
        """计算负载统计数据"""
        for sat_id in self.target_satellites:
            if sat_id in self.load_history and self.load_history[sat_id]:
                history = self.load_history[sat_id]
                
                # 提取各项指标
                queue_lengths = [h['queue_length'] for h in history]
                cpu_utils = [h['cpu_utilization'] for h in history]
                battery_levels = [h['battery_percentage'] for h in history]
                processing_counts = [h['processing_count'] for h in history]
                
                self.statistics[sat_id] = {
                    # 队列统计
                    'queue_mean': np.mean(queue_lengths),
                    'queue_std': np.std(queue_lengths),
                    'queue_max': np.max(queue_lengths),
                    'queue_min': np.min(queue_lengths),
                    'queue_p90': np.percentile(queue_lengths, 90),
                    
                    # CPU利用率统计
                    'cpu_mean': np.mean(cpu_utils),
                    'cpu_std': np.std(cpu_utils),
                    'cpu_max': np.max(cpu_utils),
                    'cpu_min': np.min(cpu_utils),
                    
                    # 电池统计
                    'battery_mean': np.mean(battery_levels),
                    'battery_min': np.min(battery_levels),
                    'battery_final': battery_levels[-1] if battery_levels else 0,
                    
                    # 处理任务统计
                    'processing_mean': np.mean(processing_counts),
                    'processing_max': np.max(processing_counts),
                    
                    # 总体统计
                    'total_completed': history[-1]['tasks_completed'] if history else 0,
                    'total_dropped': history[-1]['tasks_dropped'] if history else 0,
                    'total_energy': history[-1]['energy_consumed'] if history else 0
                }
    
    def export_to_csv(self, output_dir: str = "load_tracking_results"):
        """
        导出负载数据到CSV文件
        
        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        for sat_id, history in self.load_history.items():
            if history:
                df = pd.DataFrame(history)
                filename = output_path / f"satellite_{sat_id}_load.csv"
                df.to_csv(filename, index=False, encoding='utf-8')
                self.logger.info(f"导出卫星 {sat_id} 负载数据到 {filename}")
        
        # 导出统计数据
        stats_df = pd.DataFrame(self.statistics).T
        stats_df.index.name = 'satellite_id'
        stats_filename = output_path / "load_statistics.csv"
        stats_df.to_csv(stats_filename, encoding='utf-8')
        self.logger.info(f"导出统计数据到 {stats_filename}")
    
    def export_to_json(self, output_file: str = "load_tracking_results.json"):
        """
        导出负载数据到JSON文件
        
        Args:
            output_file: 输出文件路径
        """
        output_data = {
            'metadata': {
                'target_satellites': self.target_satellites,
                'total_timeslots': len(self.load_history[self.target_satellites[0]]) if self.target_satellites else 0,
                'timeslot_duration_s': 3,
                'timestamp': datetime.now().isoformat()
            },
            'load_history': self.load_history,
            'statistics': self.statistics
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2)
        
        self.logger.info(f"导出负载数据到 {output_file}")
    
    def plot_loads(self, output_dir: str = "load_tracking_plots"):
        """
        绘制负载变化图
        
        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 为每个卫星绘制负载图
        for sat_id in self.target_satellites:
            if sat_id not in self.load_history or not self.load_history[sat_id]:
                continue
                
            history = self.load_history[sat_id]
            timeslots = [h['timeslot'] for h in history]
            
            # 创建子图
            fig, axes = plt.subplots(3, 2, figsize=(15, 12))
            fig.suptitle(f'卫星 {sat_id} 负载分析', fontsize=16)
            
            # 1. 队列长度
            axes[0, 0].plot(timeslots, [h['queue_length'] for h in history], 'b-', linewidth=0.5)
            axes[0, 0].set_title('队列长度')
            axes[0, 0].set_xlabel('时隙')
            axes[0, 0].set_ylabel('任务数')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. CPU利用率
            axes[0, 1].plot(timeslots, [h['cpu_utilization']*100 for h in history], 'g-', linewidth=0.5)
            axes[0, 1].set_title('CPU利用率')
            axes[0, 1].set_xlabel('时隙')
            axes[0, 1].set_ylabel('利用率 (%)')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 电池能量
            axes[1, 0].plot(timeslots, [h['battery_percentage'] for h in history], 'r-', linewidth=0.5)
            axes[1, 0].set_title('电池能量')
            axes[1, 0].set_xlabel('时隙')
            axes[1, 0].set_ylabel('电池电量 (%)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 处理中的任务数
            axes[1, 1].plot(timeslots, [h['processing_count'] for h in history], 'm-', linewidth=0.5)
            axes[1, 1].set_title('并行处理任务数')
            axes[1, 1].set_xlabel('时隙')
            axes[1, 1].set_ylabel('任务数')
            axes[1, 1].grid(True, alpha=0.3)
            
            # 5. 累计完成任务
            axes[2, 0].plot(timeslots, [h['tasks_completed'] for h in history], 'c-', linewidth=0.5)
            axes[2, 0].set_title('累计完成任务数')
            axes[2, 0].set_xlabel('时隙')
            axes[2, 0].set_ylabel('任务数')
            axes[2, 0].grid(True, alpha=0.3)
            
            # 6. 系统中总任务数
            axes[2, 1].plot(timeslots, [h['total_in_system'] for h in history], 'y-', linewidth=0.5)
            axes[2, 1].set_title('系统中总任务数（队列+处理中）')
            axes[2, 1].set_xlabel('时隙')
            axes[2, 1].set_ylabel('任务数')
            axes[2, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            filename = output_path / f"satellite_{sat_id}_load_plot.png"
            plt.savefig(filename, dpi=150, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"保存卫星 {sat_id} 负载图到 {filename}")
        
        # 绘制对比图
        self._plot_comparison(output_path)
    
    def _plot_comparison(self, output_path: Path):
        """绘制卫星间对比图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('卫星负载对比分析', fontsize=16)
        
        colors = ['b', 'g', 'r', 'm', 'c']
        
        for idx, sat_id in enumerate(self.target_satellites):
            if sat_id not in self.load_history or not self.load_history[sat_id]:
                continue
                
            history = self.load_history[sat_id]
            timeslots = [h['timeslot'] for h in history]
            color = colors[idx % len(colors)]
            
            # 1. 队列长度对比
            axes[0, 0].plot(timeslots, [h['queue_length'] for h in history], 
                          color=color, linewidth=0.5, label=f'Sat {sat_id}', alpha=0.7)
            
            # 2. CPU利用率对比
            axes[0, 1].plot(timeslots, [h['cpu_utilization']*100 for h in history], 
                          color=color, linewidth=0.5, label=f'Sat {sat_id}', alpha=0.7)
            
            # 3. 电池能量对比
            axes[1, 0].plot(timeslots, [h['battery_percentage'] for h in history], 
                          color=color, linewidth=0.5, label=f'Sat {sat_id}', alpha=0.7)
            
            # 4. 累计完成任务对比
            axes[1, 1].plot(timeslots, [h['tasks_completed'] for h in history], 
                          color=color, linewidth=0.5, label=f'Sat {sat_id}', alpha=0.7)
        
        axes[0, 0].set_title('队列长度对比')
        axes[0, 0].set_xlabel('时隙')
        axes[0, 0].set_ylabel('任务数')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].set_title('CPU利用率对比')
        axes[0, 1].set_xlabel('时隙')
        axes[0, 1].set_ylabel('利用率 (%)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        axes[1, 0].set_title('电池能量对比')
        axes[1, 0].set_xlabel('时隙')
        axes[1, 0].set_ylabel('电池电量 (%)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].set_title('累计完成任务对比')
        axes[1, 1].set_xlabel('时隙')
        axes[1, 1].set_ylabel('任务数')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        filename = output_path / "satellites_comparison.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"保存对比图到 {filename}")
    
    def print_summary(self):
        """打印负载统计摘要"""
        print("\n" + "="*80)
        print(" 卫星负载统计摘要")
        print("="*80)
        
        for sat_id in self.target_satellites:
            if sat_id in self.statistics and self.statistics[sat_id]:
                stats = self.statistics[sat_id]
                print(f"\n卫星 {sat_id}:")
                print(f"  队列长度: 平均={stats['queue_mean']:.1f}, 最大={stats['queue_max']:.0f}, P90={stats['queue_p90']:.0f}")
                print(f"  CPU利用率: 平均={stats['cpu_mean']*100:.1f}%, 最大={stats['cpu_max']*100:.1f}%")
                print(f"  电池电量: 平均={stats['battery_mean']:.1f}%, 最终={stats['battery_final']:.1f}%")
                print(f"  任务处理: 完成={stats['total_completed']:.0f}, 丢弃={stats['total_dropped']:.0f}")
                print(f"  能量消耗: {stats['total_energy']:.1f} J")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='跟踪卫星负载变化')
    parser.add_argument('--timeslots', type=int, default=2000,
                       help='仿真时隙数 (default: 2000)')
    parser.add_argument('--satellites', type=int, nargs='+', 
                       default=[111, 112, 121, 131, 181],
                       help='要跟踪的卫星ID列表')
    parser.add_argument('--output-json', type=str, default='load_tracking_results.json',
                       help='JSON输出文件')
    parser.add_argument('--output-csv-dir', type=str, default='load_tracking_results',
                       help='CSV输出目录')
    parser.add_argument('--plot', action='store_true',
                       help='是否生成图表')
    parser.add_argument('--plot-dir', type=str, default='load_tracking_plots',
                       help='图表输出目录')
    
    args = parser.parse_args()
    
    print("="*80)
    print(" 卫星负载跟踪程序")
    print("="*80)
    print(f"跟踪卫星: {args.satellites}")
    print(f"仿真时隙: {args.timeslots}")
    print("="*80)
    
    # 创建跟踪器
    tracker = SatelliteLoadTracker(target_satellites=args.satellites)
    
    # 跟踪负载
    print("\n开始跟踪负载...")
    load_history = tracker.track_loads(total_timeslots=args.timeslots)
    
    # 打印摘要
    tracker.print_summary()
    
    # 导出数据
    print("\n导出数据...")
    tracker.export_to_json(args.output_json)
    tracker.export_to_csv(args.output_csv_dir)
    
    # 生成图表
    if args.plot:
        print("\n生成图表...")
        tracker.plot_loads(args.plot_dir)
    
    print("\n完成！")
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())