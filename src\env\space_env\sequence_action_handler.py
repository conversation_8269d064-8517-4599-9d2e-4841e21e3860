"""
序列动作处理器
处理Transformer-MAPPO的序列动作，验证可行性并执行任务分配
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ActionTarget:
    """动作目标定义"""
    target_type: str  # 'local', 'neighbor', 'cloud'
    target_id: Optional[str] = None
    target_index: Optional[int] = None
    is_valid: bool = True
    reason: Optional[str] = None


class SequenceActionHandler:
    """
    序列动作处理器
    负责解析、验证和执行Transformer生成的动作序列
    """
    
    def __init__(self, config: Dict):
        """
        初始化动作处理器
        
        Args:
            config: 环境配置
        """
        self.config = config
        
        # 动作空间参数
        self.max_visible_neighbors = config.get('max_visible_neighbors', 10)
        self.num_cloud_targets = config.get('num_cloud_targets', 5)
        self.max_queue_size = config.get('max_queue_size', 20)
        
        # 动作索引映射
        self.action_map = self._build_action_map()
        
        # 统计信息
        self.reset_statistics()
    
    def _build_action_map(self) -> Dict[int, ActionTarget]:
        """构建动作索引到目标的映射"""
        action_map = {}
        idx = 0
        
        # 0: 本地处理
        action_map[idx] = ActionTarget(target_type='local')
        idx += 1
        
        # 1-N: 邻居卫星
        for i in range(self.max_visible_neighbors):
            action_map[idx] = ActionTarget(
                target_type='neighbor',
                target_index=i
            )
            idx += 1
        
        # N+1-M: 云中心
        for i in range(self.num_cloud_targets):
            action_map[idx] = ActionTarget(
                target_type='cloud',
                target_index=i
            )
            idx += 1
        
        return action_map
    
    def reset_statistics(self):
        """重置统计信息"""
        self.statistics = {
            'total_actions': 0,
            'valid_actions': 0,
            'invalid_actions': 0,
            'local_assignments': 0,
            'neighbor_assignments': 0,
            'cloud_assignments': 0,
            'validation_failures': {
                'out_of_range': 0,
                'no_visibility': 0,
                'target_overload': 0,
                'resource_insufficient': 0
            }
        }
    
    def process_action_sequence(self,
                               action_sequence: np.ndarray,
                               task_queue: List,
                               agent_id: str,
                               visibility_info: Dict,
                               resource_info: Optional[Dict] = None) -> List[Dict]:
        """
        处理动作序列
        
        Args:
            action_sequence: 动作序列数组 [action_0, ..., action_N]
            task_queue: 任务队列
            agent_id: 执行动作的智能体ID
            visibility_info: 可见性信息
            resource_info: 资源信息（可选）
        
        Returns:
            处理结果列表，每个元素包含任务分配信息
        """
        results = []
        
        # 获取实际任务数（去除padding）
        actual_tasks = min(len(task_queue), len(action_sequence))
        
        for i in range(actual_tasks):
            task = task_queue[i]
            action = action_sequence[i]
            
            # 解析和验证动作
            target = self.parse_action(action, agent_id, visibility_info)
            
            # 构建分配结果
            assignment = {
                'task': task,
                'action': action,
                'target': target,
                'success': target.is_valid,
                'agent_id': agent_id,
                'task_index': i  # 添加任务索引
            }
            
            # 更新统计
            self._update_statistics(target)
            
            results.append(assignment)
        
        return results
    
    def parse_action(self, 
                    action: int,
                    agent_id: str,
                    visibility_info: Dict) -> ActionTarget:
        """
        解析单个动作
        
        Args:
            action: 动作索引
            agent_id: 智能体ID
            visibility_info: 可见性信息
        
        Returns:
            动作目标
        """
        # 检查动作范围
        if action not in self.action_map:
            return ActionTarget(
                target_type='invalid',
                is_valid=False,
                reason='action_out_of_range'
            )
        
        target = self.action_map[action]
        
        # 验证动作可行性
        if target.target_type == 'local':
            # 本地处理总是可行
            target.target_id = agent_id
            target.is_valid = True
            
        elif target.target_type == 'neighbor':
            # 检查邻居可见性
            visible_neighbors = visibility_info.get('visible_neighbors', [])
            
            if target.target_index < len(visible_neighbors):
                target.target_id = visible_neighbors[target.target_index]
                target.is_valid = True
            else:
                target.is_valid = False
                target.reason = 'neighbor_not_visible'
                
        elif target.target_type == 'cloud':
            # 检查地面站可见性
            has_ground_visibility = visibility_info.get('has_ground_visibility', False)
            
            if has_ground_visibility:
                target.target_id = f"cloud_{target.target_index}"
                target.is_valid = True
            else:
                target.is_valid = False
                target.reason = 'no_ground_visibility'
        
        return target
    
    def validate_batch_actions(self,
                              all_actions: Dict[str, np.ndarray],
                              all_task_queues: Dict[str, List],
                              global_visibility: Dict,
                              global_resources: Optional[Dict] = None) -> Dict[str, List]:
        """
        批量验证所有智能体的动作
        
        Args:
            all_actions: 所有智能体的动作 {agent_id: action_sequence}
            all_task_queues: 所有任务队列
            global_visibility: 全局可见性信息
            global_resources: 全局资源信息
        
        Returns:
            验证结果
        """
        validated_results = {}
        
        # 跟踪每个目标的负载
        target_loads = {}
        
        for agent_id, action_seq in all_actions.items():
            task_queue = all_task_queues.get(agent_id, [])
            visibility_info = self._extract_agent_visibility(agent_id, global_visibility)
            
            # 处理该智能体的动作序列
            results = self.process_action_sequence(
                action_seq, task_queue, agent_id, 
                visibility_info, global_resources
            )
            
            # 负载均衡验证
            for result in results:
                if result['success'] and result['target'].target_type != 'local':
                    target_id = result['target'].target_id
                    
                    if target_id not in target_loads:
                        target_loads[target_id] = 0
                    
                    # 检查目标负载
                    max_load = self.config.get('max_target_load', 50)
                    if target_loads[target_id] >= max_load:
                        result['success'] = False
                        result['target'].is_valid = False
                        result['target'].reason = 'target_overloaded'
                        self.statistics['validation_failures']['target_overload'] += 1
                    else:
                        target_loads[target_id] += 1
            
            validated_results[agent_id] = results
        
        return validated_results
    
    def _extract_agent_visibility(self, agent_id: str, global_visibility: Dict) -> Dict:
        """提取特定智能体的可见性信息"""
        # 从全局可见性信息中提取该智能体相关的部分
        agent_idx = self._get_agent_index(agent_id)
        
        visibility_info = {
            'visible_neighbors': [],
            'has_ground_visibility': False
        }
        
        # 提取可见邻居
        if 'satellite_to_satellite' in global_visibility:
            sat_to_sat = global_visibility['satellite_to_satellite']
            if agent_idx < len(sat_to_sat):
                visible_indices = np.where(sat_to_sat[agent_idx] > 0)[0]
                visibility_info['visible_neighbors'] = [
                    f"sat_{111 + idx}" for idx in visible_indices
                ]
        
        # 检查地面站可见性
        if 'satellite_to_ground' in global_visibility:
            sat_to_ground = global_visibility['satellite_to_ground']
            if agent_idx < len(sat_to_ground):
                visibility_info['has_ground_visibility'] = np.any(sat_to_ground[agent_idx] > 0)
        
        return visibility_info
    
    def _get_agent_index(self, agent_id: str) -> int:
        """从agent_id获取索引"""
        # agent_id格式: "sat_111", "sat_112", ...
        sat_num = int(agent_id.split('_')[1])
        return sat_num - 111
    
    def _update_statistics(self, target: ActionTarget):
        """更新统计信息"""
        self.statistics['total_actions'] += 1
        
        if target.is_valid:
            self.statistics['valid_actions'] += 1
            
            if target.target_type == 'local':
                self.statistics['local_assignments'] += 1
            elif target.target_type == 'neighbor':
                self.statistics['neighbor_assignments'] += 1
            elif target.target_type == 'cloud':
                self.statistics['cloud_assignments'] += 1
        else:
            self.statistics['invalid_actions'] += 1
            
            if target.reason == 'action_out_of_range':
                self.statistics['validation_failures']['out_of_range'] += 1
            elif target.reason in ['neighbor_not_visible', 'no_ground_visibility']:
                self.statistics['validation_failures']['no_visibility'] += 1
            elif target.reason == 'target_overloaded':
                self.statistics['validation_failures']['target_overload'] += 1
            elif target.reason == 'insufficient_resources':
                self.statistics['validation_failures']['resource_insufficient'] += 1
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return self.statistics.copy()
    
    def get_action_mask(self,
                       agent_id: str,
                       task_queue_size: int,
                       visibility_info: Dict,
                       resource_info: Optional[Dict] = None) -> np.ndarray:
        """
        生成动作掩码
        
        Args:
            agent_id: 智能体ID
            task_queue_size: 任务队列大小
            visibility_info: 可见性信息
            resource_info: 资源信息
        
        Returns:
            动作掩码矩阵 [max_queue_size, num_actions]
        """
        num_actions = len(self.action_map)
        mask = np.zeros((self.max_queue_size, num_actions), dtype=np.int8)
        
        # 对每个任务位置生成掩码
        for task_idx in range(min(task_queue_size, self.max_queue_size)):
            # 本地处理总是可行
            mask[task_idx, 0] = 1
            
            # 邻居卫星
            visible_neighbors = visibility_info.get('visible_neighbors', [])
            for i in range(min(len(visible_neighbors), self.max_visible_neighbors)):
                mask[task_idx, i + 1] = 1
            
            # 云中心（需要地面站可见）
            if visibility_info.get('has_ground_visibility', False):
                cloud_start_idx = 1 + self.max_visible_neighbors
                for i in range(self.num_cloud_targets):
                    mask[task_idx, cloud_start_idx + i] = 1
        
        return mask
    
    def decode_action_sequence(self, action_sequence: np.ndarray) -> List[str]:
        """
        将动作序列解码为可读形式
        
        Args:
            action_sequence: 动作序列
        
        Returns:
            解码后的动作描述列表
        """
        descriptions = []
        
        for action in action_sequence:
            if action in self.action_map:
                target = self.action_map[action]
                if target.target_type == 'local':
                    desc = "Local Processing"
                elif target.target_type == 'neighbor':
                    desc = f"Forward to Neighbor {target.target_index}"
                elif target.target_type == 'cloud':
                    desc = f"Send to Cloud {target.target_index}"
                else:
                    desc = f"Unknown Action {action}"
            else:
                desc = f"Invalid Action {action}"
            
            descriptions.append(desc)
        
        return descriptions