"""
Utility functions for MAPPO Transformer
工具函数 - 模仿参考代码的工具类
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import os
from typing import Dict, List, Tuple, Any

from src.agent.mappo_trans.config import MAPPOConfig


# ==================== 观测处理工具 ====================

def extract_features_from_obs(obs: Dict[str, np.ndarray]) -> np.ndarray:
    """
    从PettingZoo观测字典中提取特征并展平
    
    Args:
        obs: 单个智能体的观测字典
        
    Returns:
        展平的特征向量
    """
    features = []
    
    # 自身状态 [12]
    features.append(obs['own_state'].flatten())
    
    # 任务队列 [20, 8] -> [160]
    features.append(obs['task_queue'].flatten())
    
    # 任务掩码 [20]
    features.append(obs['task_mask'].flatten())
    
    # 动作掩码 [20, 16] -> [320]
    features.append(obs['action_mask'].flatten())
    
    # 邻居状态 [10, 5] -> [50]
    features.append(obs['neighbor_states'].flatten())
    
    # 通信质量 [10]
    features.append(obs['comm_quality'].flatten())
    
    # 时间信息 [3]
    features.append(obs['time_info'].flatten())
    
    # 拼接所有特征
    feature_vector = np.concatenate(features, axis=0)
    
    return feature_vector.astype(np.float32)


def extract_sequence_info_from_obs(obs: Dict[str, np.ndarray]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    从观测中提取序列信息用于Transformer
    
    Args:
        obs: 单个智能体的观测字典
        
    Returns:
        task_sequence: 任务序列 [20, 8]
        task_mask: 任务掩码 [20] 
        action_mask: 动作掩码 [20, 16]
    """
    task_sequence = obs['task_queue']  # [20, 8]
    task_mask = obs['task_mask']       # [20]
    action_mask = obs['action_mask']   # [20, 16]
    
    return task_sequence, task_mask, action_mask


def build_global_state(observations: Dict[str, Dict]) -> np.ndarray:
    """
    构建全局状态用于集中式批评家
    
    Args:
        observations: 所有智能体的观测字典
        
    Returns:
        全局状态向量
    """
    global_features = []
    
    for agent_id in sorted(observations.keys()):
        obs = observations[agent_id]
        # 只使用部分信息构建全局状态（避免维度过大）
        agent_features = []
        agent_features.append(obs['own_state'])  # [12]
        agent_features.append(np.array([np.sum(obs['task_mask'])]))  # 任务数量转为数组
        agent_features.append(np.mean(obs['task_queue'], axis=0))  # 任务特征均值 [8]
        
        global_features.append(np.concatenate(agent_features))
    
    # 拼接所有智能体的特征
    global_state = np.concatenate(global_features, axis=0)
    
    return global_state.astype(np.float32)


# ==================== 归一化工具（模仿参考代码） ====================

class RunningMeanStd:
    """动态计算均值和标准差"""
    def __init__(self, shape):
        self.n = 0
        self.mean = np.zeros(shape)
        self.S = np.zeros(shape)
        self.std = np.sqrt(self.S)

    def update(self, x):
        x = np.array(x)
        self.n += 1
        if self.n == 1:
            self.mean = x
            self.std = x
        else:
            old_mean = self.mean.copy()
            self.mean = old_mean + (x - old_mean) / self.n
            self.S = self.S + (x - old_mean) * (x - self.mean)
            self.std = np.sqrt(self.S / self.n)


class Normalization:
    """归一化类"""
    def __init__(self, shape):
        self.running_ms = RunningMeanStd(shape=shape)

    def __call__(self, x, update=True):
        if update:
            self.running_ms.update(x)
        x = (x - self.running_ms.mean) / (self.running_ms.std + 1e-8)
        return x


class RewardScaling:
    """奖励缩放"""
    def __init__(self, shape, gamma):
        self.shape = shape
        self.gamma = gamma
        self.running_ms = RunningMeanStd(shape=self.shape)
        self.R = np.zeros(self.shape)

    def __call__(self, x):
        self.R = self.gamma * self.R + x
        self.running_ms.update(self.R)
        x = x / (self.running_ms.std + 1e-8)
        return x

    def reset(self):
        self.R = np.zeros(self.shape)


# ==================== 网络初始化工具 ====================

def orthogonal_init(layer, gain=1.0):
    """正交初始化"""
    torch.nn.init.orthogonal_(layer.weight, gain=gain)
    torch.nn.init.constant_(layer.bias, 0)


# ==================== 日志和可视化工具 ====================

def log_metrics(timeslot: int, metrics: Dict[str, Any], config: MAPPOConfig):
    """记录训练指标"""
    if config.verbose and timeslot % config.log_freq == 0:
        print(f"\n{'='*60}")
        print(f"时隙 {timeslot:4d}/{config.max_timeslots} | TIMESLOT {timeslot}")
        print(f"{'='*60}")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"{key}: {value:7.3f} | ", end="")
            else:
                print(f"{key}: {value:3d} | ", end="")
        print()  # 换行
        print(f"{'='*60}")


def save_timeslot_results(timeslot: int, results: Dict[str, Any], file_path: str):
    """保存时隙结果到文件"""
    with open(file_path, 'a') as f:
        line = f"{timeslot}," + ",".join([str(v) for v in results.values()]) + "\n"
        f.write(line)


def plot_training_curves(rewards: List[float], config: MAPPOConfig, tag: str = "train"):
    """绘制训练曲线"""
    plt.figure(figsize=(10, 6))
    plt.plot(rewards)
    plt.title(f'MAPPO Training Rewards - {tag}')
    plt.xlabel('Iteration')
    plt.ylabel('Average Reward')
    plt.grid(True)
    
    save_path = config.get_log_path(f'rewards_{tag}.png')
    plt.savefig(save_path)
    plt.close()
    
    print(f"Training curve saved to {save_path}")


def plot_rewards(rewards: List[float], config: MAPPOConfig, tag: str = "train"):
    """绘制奖励曲线（兼容参考代码接口）"""
    plot_training_curves(rewards, config, tag)


# ==================== 多智能体工具函数 ====================

def aggregate_agent_rewards(rewards: Dict[str, float]) -> Dict[str, float]:
    """聚合智能体奖励"""
    total_reward = sum(rewards.values())
    avg_reward = total_reward / len(rewards)
    max_reward = max(rewards.values())
    min_reward = min(rewards.values())
    
    return {
        'total_reward': total_reward,
        'avg_reward': avg_reward,
        'max_reward': max_reward,
        'min_reward': min_reward
    }


def count_agent_tasks(observations: Dict[str, Dict]) -> Dict[str, int]:
    """统计每个智能体的任务数量"""
    task_counts = {}
    for agent_id, obs in observations.items():
        task_counts[agent_id] = int(np.sum(obs['task_mask']))
    return task_counts


def calculate_system_utilization(observations: Dict[str, Dict]) -> float:
    """计算系统整体利用率"""
    total_utilization = 0
    for agent_id, obs in observations.items():
        # 从own_state中提取CPU利用率（假设是第0个特征）
        cpu_usage = obs['own_state'][0]
        total_utilization += cpu_usage
    
    avg_utilization = total_utilization / len(observations)
    return avg_utilization


# ==================== 动作处理工具 ====================

def apply_action_mask(logits: torch.Tensor, action_mask: torch.Tensor) -> torch.Tensor:
    """
    应用动作掩码到logits
    
    Args:
        logits: [batch_size, seq_len, action_dim]
        action_mask: [batch_size, seq_len, action_dim]
        
    Returns:
        masked_logits: 掩码后的logits
    """
    # 将不可行动作的logits设为很小的值
    masked_logits = logits + (1 - action_mask.float()) * (-1e9)
    return masked_logits


def sample_action_sequence(dist, task_mask: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    从分布中采样动作序列，考虑任务掩码
    
    Args:
        dist: 动作分布
        task_mask: 任务掩码 [batch_size, seq_len]
        
    Returns:
        actions: 采样的动作 [batch_size, seq_len]
        log_probs: 对数概率 [batch_size, seq_len]
    """
    actions = dist.sample()  # [batch_size, seq_len]
    log_probs = dist.log_prob(actions)  # [batch_size, seq_len]
    
    # 对padding位置的log_prob设为0
    log_probs = log_probs * task_mask.float()
    
    return actions, log_probs


# ==================== 文件和目录工具 ====================

def ensure_dir(path: str):
    """确保目录存在"""
    os.makedirs(path, exist_ok=True)


def save_config(config: MAPPOConfig, file_path: str):
    """保存配置到文件"""
    import json
    config_dict = {
        'env_name': config.env_name,
        'algo_name': config.algo_name,
        'n_agents': config.n_agents,
        'hidden_dim': config.hidden_dim,
        'lr_a': config.lr_a,
        'lr_c': config.lr_c,
        'gamma': config.gamma,
        'lamda': config.lamda,
        'epsilon': config.epsilon,
        'total_timesteps': config.total_timesteps
    }
    
    with open(file_path, 'w') as f:
        json.dump(config_dict, f, indent=2)


# ==================== 测试函数 ====================

if __name__ == "__main__":
    from src.agent.mappo_trans.config import get_default_config
    
    # 测试配置
    config = get_default_config()
    print(f"Config loaded: {config.algo_name}")
    
    # 测试观测处理
    dummy_obs = {
        'own_state': np.random.randn(12),
        'task_queue': np.random.randn(20, 8),
        'task_mask': np.random.randint(0, 2, 20),
        'action_mask': np.random.randint(0, 2, (20, 16)),
        'neighbor_states': np.random.randn(10, 5),
        'comm_quality': np.random.randn(10),
        'time_info': np.random.randn(3)
    }
    
    # 测试特征提取
    features = extract_features_from_obs(dummy_obs)
    print(f"Extracted features shape: {features.shape}")
    print(f"Expected obs_dim: {config.obs_dim}")
    
    # 测试序列信息提取
    task_seq, task_mask, action_mask = extract_sequence_info_from_obs(dummy_obs)
    print(f"Task sequence shape: {task_seq.shape}")
    print(f"Task mask shape: {task_mask.shape}")
    print(f"Action mask shape: {action_mask.shape}")
    
    # 测试全局状态构建
    multi_obs = {f"sat_{111+i}": dummy_obs for i in range(3)}
    global_state = build_global_state(multi_obs)
    print(f"Global state shape: {global_state.shape}")
    
    print("Utils test completed!")