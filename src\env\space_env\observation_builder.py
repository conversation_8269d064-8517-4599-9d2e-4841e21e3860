"""
观测构建器
负责构建符合Transformer-MAPPO需求的观测空间，包括padding和masking
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ObservationConfig:
    """观测配置"""
    max_queue_size: int = 20
    task_feature_dim: int = 8
    state_feature_dim: int = 12
    neighbor_feature_dim: int = 5
    num_action_targets: int = 16
    max_visible_neighbors: int = 10


class ObservationBuilder:
    """
    观测构建器
    将环境状态转换为适合Transformer输入的观测格式
    """
    
    def __init__(self,
                 max_queue_size: int = 20,
                 task_feature_dim: int = 8,
                 state_feature_dim: int = 12,
                 neighbor_feature_dim: int = 5,
                 num_action_targets: int = 16,
                 max_visible_neighbors: int = 10):
        """
        初始化观测构建器
        
        Args:
            max_queue_size: 最大任务队列长度（用于padding）
            task_feature_dim: 任务特征维度
            state_feature_dim: 状态特征维度
            neighbor_feature_dim: 邻居特征维度
            num_action_targets: 动作目标数量
            max_visible_neighbors: 最大可见邻居数
        """
        self.max_queue_size = max_queue_size
        self.task_feature_dim = task_feature_dim
        self.state_feature_dim = state_feature_dim
        self.neighbor_feature_dim = neighbor_feature_dim
        self.num_action_targets = num_action_targets
        self.max_visible_neighbors = max_visible_neighbors
        
        # 归一化参数
        self.normalization_params = self._get_normalization_params()
    
    def _get_normalization_params(self) -> Dict:
        """获取归一化参数"""
        return {
            'task': {
                'data_size_max': 100.0,  # MB
                'complexity_max': 1000.0,  # cycles/bit
                'priority_max': 5.0,
                'deadline_max': 3600.0,  # seconds
                'lat_max': 90.0,
                'lon_max': 180.0
            },
            'state': {
                'cpu_max': 100.0,  # %
                'memory_max': 100.0,  # %
                'battery_max': 100.0,  # %
                'queue_max': self.max_queue_size,
                'tasks_max': 1000.0,
                'energy_max': 10000.0  # J
            },
            'neighbor': {
                'distance_max': 5500.0,  # km
                'comm_rate_max': 1000.0,  # Mbps
                'load_max': 100.0  # %
            }
        }
    
    def build_observation(self,
                         agent_id: str,
                         satellite,
                         task_queue: List,
                         visibility_matrices: Dict,
                         comm_matrix: Optional[np.ndarray],
                         current_timeslot: int,
                         max_timeslots: int,
                         satellite_index: int,
                         all_satellites: Dict) -> Dict[str, np.ndarray]:
        """
        构建单个智能体的观测
        
        Args:
            agent_id: 智能体ID
            satellite: 卫星对象
            task_queue: 任务队列
            visibility_matrices: 可见性矩阵
            comm_matrix: 通信质量矩阵
            current_timeslot: 当前时隙
            max_timeslots: 最大时隙数
            satellite_index: 卫星索引
            all_satellites: 所有卫星字典
        
        Returns:
            观测字典
        """
        observation = {}
        
        # 1. 构建自身状态
        observation['own_state'] = self._build_own_state(
            satellite, current_timeslot, max_timeslots
        )
        
        # 2. 构建任务队列（关键：包含padding和masking）
        task_features, task_mask = self._build_task_queue(task_queue)
        observation['task_queue'] = task_features
        observation['task_mask'] = task_mask
        
        # 3. 构建动作掩码
        observation['action_mask'] = self._build_action_mask(
            task_queue, satellite_index, visibility_matrices
        )
        
        # 4. 构建邻居状态
        observation['neighbor_states'] = self._build_neighbor_states(
            satellite_index, visibility_matrices, all_satellites
        )
        
        # 5. 构建通信质量
        observation['comm_quality'] = self._build_comm_quality(
            satellite_index, visibility_matrices, comm_matrix
        )
        
        # 6. 构建时间信息
        observation['time_info'] = self._build_time_info(
            current_timeslot, max_timeslots
        )
        
        return observation
    
    def _build_own_state(self, satellite, current_timeslot: int, max_timeslots: int) -> np.ndarray:
        """
        构建卫星自身状态
        
        Returns:
            归一化的状态向量 [state_feature_dim]
        """
        # 获取卫星状态（使用实际存在的方法）
        queue_status = satellite.get_queue_status()
        energy_status = satellite.get_energy_status()
        statistics = satellite.get_statistics()
        
        norm_params = self.normalization_params['state']
        
        state = np.zeros(self.state_feature_dim, dtype=np.float32)
        
        # 基础资源状态
        state[0] = statistics.get('avg_utilization', 0) / norm_params['cpu_max']
        state[1] = statistics.get('avg_utilization', 0) / norm_params['memory_max']  # 简化：使用相同的利用率
        state[2] = energy_status.get('battery_level', 50) / norm_params['battery_max']
        
        # 任务处理状态
        state[3] = queue_status.get('queue_length', 0) / norm_params['queue_max']
        state[4] = queue_status.get('num_waiting', 0) / 10.0  # 最多10个等待任务
        state[5] = statistics.get('total_completed', 0) / norm_params['tasks_max']
        state[6] = statistics.get('total_dropped', 0) / norm_params['tasks_max']
        
        # 能量状态
        state[7] = energy_status.get('total_consumed', 0) / norm_params['energy_max']
        state[8] = 1.0 if energy_status.get('in_sunlight', False) else 0.0
        
        # 时间进度
        state[9] = current_timeslot / max_timeslots
        state[10] = (max_timeslots - current_timeslot) / max_timeslots
        
        # 负载指标
        state[11] = statistics.get('avg_processing_time', 0) / 100.0  # 归一化平均处理时间
        
        # 确保值在[0, 1]范围内
        state = np.clip(state, 0, 1)
        
        return state
    
    def _build_task_queue(self, task_queue: List) -> Tuple[np.ndarray, np.ndarray]:
        """
        构建任务队列特征（包含padding）
        
        Returns:
            task_features: 任务特征矩阵 [max_queue_size, task_feature_dim]
            task_mask: 任务掩码 [max_queue_size]，1表示真实任务，0表示padding
        """
        task_features = np.zeros((self.max_queue_size, self.task_feature_dim), dtype=np.float32)
        task_mask = np.zeros(self.max_queue_size, dtype=np.int8)
        
        norm_params = self.normalization_params['task']
        
        # 填充真实任务
        for i, task in enumerate(task_queue[:self.max_queue_size]):
            # 编码任务特征
            task_features[i] = self._encode_task(task, norm_params)
            task_mask[i] = 1  # 标记为真实任务
        
        return task_features, task_mask
    
    def _encode_task(self, task, norm_params: Dict) -> np.ndarray:
        """
        编码单个任务
        
        Returns:
            任务特征向量 [task_feature_dim]
        """
        features = np.zeros(self.task_feature_dim, dtype=np.float32)
        
        # 任务类型（one-hot或归一化）
        if hasattr(task, 'type_id'):
            features[0] = task.type_id.value / 3.0  # 假设有3种类型
        else:
            features[0] = 0.5  # 默认值
        
        # 优先级
        features[1] = min(task.priority / norm_params['priority_max'], 1.0)
        
        # 数据大小
        features[2] = min(task.data_size_mb / norm_params['data_size_max'], 1.0)
        
        # 计算复杂度
        features[3] = min(task.complexity_cycles_per_bit / norm_params['complexity_max'], 1.0)
        
        # 截止时间（剩余时间）
        remaining_time = max(0, task.deadline_timestamp - task.generation_time)
        features[4] = min(remaining_time / norm_params['deadline_max'], 1.0)
        
        # 地理位置（归一化经纬度）
        if hasattr(task, 'coordinates'):
            features[5] = (task.coordinates[0] + norm_params['lat_max']) / (2 * norm_params['lat_max'])
            features[6] = (task.coordinates[1] + norm_params['lon_max']) / (2 * norm_params['lon_max'])
        else:
            features[5] = 0.5
            features[6] = 0.5
        
        # 紧急程度（基于截止时间和优先级的组合）
        urgency = (task.priority / norm_params['priority_max']) * (1.0 - features[4])
        features[7] = min(urgency, 1.0)
        
        return features
    
    def _build_action_mask(self,
                           task_queue: List,
                           satellite_index: int,
                           visibility_matrices: Dict) -> np.ndarray:
        """
        构建动作掩码
        
        Returns:
            动作掩码矩阵 [max_queue_size, num_action_targets]
        """
        mask = np.zeros((self.max_queue_size, self.num_action_targets), dtype=np.int8)
        
        # 获取可见性信息
        sat_to_sat = visibility_matrices.get('satellite_to_satellite', None)
        sat_to_ground = visibility_matrices.get('satellite_to_ground', None)
        
        # 获取可见邻居
        visible_neighbors = []
        if sat_to_sat is not None and satellite_index < len(sat_to_sat):
            visible_neighbors = np.where(sat_to_sat[satellite_index] > 0)[0].tolist()
        
        # 检查地面站可见性
        has_ground_visibility = False
        if sat_to_ground is not None and satellite_index < len(sat_to_ground):
            has_ground_visibility = np.any(sat_to_ground[satellite_index] > 0)
        
        # 为每个真实任务设置可行动作
        for i in range(min(len(task_queue), self.max_queue_size)):
            # 本地处理总是可行
            mask[i, 0] = 1
            
            # 邻居卫星（基于可见性）
            for j, neighbor_idx in enumerate(visible_neighbors[:self.max_visible_neighbors]):
                mask[i, j + 1] = 1
            
            # 云中心（需要地面站可见）
            if has_ground_visibility:
                cloud_start_idx = 1 + self.max_visible_neighbors
                for j in range(min(5, self.num_action_targets - cloud_start_idx)):
                    mask[i, cloud_start_idx + j] = 1
        
        return mask
    
    def _build_neighbor_states(self,
                               satellite_index: int,
                               visibility_matrices: Dict,
                               all_satellites: Dict) -> np.ndarray:
        """
        构建邻居卫星状态
        
        Returns:
            邻居状态矩阵 [max_visible_neighbors, neighbor_feature_dim]
        """
        neighbor_states = np.zeros((self.max_visible_neighbors, self.neighbor_feature_dim), dtype=np.float32)
        
        # 获取可见邻居
        sat_to_sat = visibility_matrices.get('satellite_to_satellite', None)
        if sat_to_sat is None or satellite_index >= len(sat_to_sat):
            return neighbor_states
        
        visible_neighbors = np.where(sat_to_sat[satellite_index] > 0)[0]
        
        norm_params = self.normalization_params['neighbor']
        
        # 填充邻居状态
        for i, neighbor_idx in enumerate(visible_neighbors[:self.max_visible_neighbors]):
            # 获取邻居卫星
            neighbor_id = f"sat_{111 + neighbor_idx}"
            if neighbor_id in all_satellites:
                neighbor_sat = all_satellites[neighbor_id]
                # 使用实际存在的方法获取邻居状态
                neighbor_queue = neighbor_sat.get_queue_status()
                neighbor_energy = neighbor_sat.get_energy_status()
                neighbor_stats = neighbor_sat.get_statistics()
                
                # 编码邻居特征
                neighbor_states[i, 0] = sat_to_sat[satellite_index, neighbor_idx]  # 可见性强度
                neighbor_states[i, 1] = neighbor_stats.get('avg_utilization', 0) / 100.0
                neighbor_states[i, 2] = neighbor_queue.get('queue_length', 0) / self.max_queue_size
                neighbor_states[i, 3] = neighbor_energy.get('battery_level', 50) / 100.0
                neighbor_states[i, 4] = 1.0 if neighbor_energy.get('in_sunlight', False) else 0.0
        
        return neighbor_states
    
    def _build_comm_quality(self,
                            satellite_index: int,
                            visibility_matrices: Dict,
                            comm_matrix: Optional[Dict]) -> np.ndarray:
        """
        构建通信质量向量
        
        Args:
            satellite_index: 卫星索引
            visibility_matrices: 可见性矩阵
            comm_matrix: 通信矩阵字典 (包含isl, satellite_ground, satellite_cloud)
        
        Returns:
            通信质量向量 [max_visible_neighbors]
        """
        comm_quality = np.zeros(self.max_visible_neighbors, dtype=np.float32)
        
        if comm_matrix is None or 'isl' not in comm_matrix:
            return comm_quality
        
        # 获取卫星间通信矩阵
        isl_comm = comm_matrix['isl']
        if 'data_rate_bps' not in isl_comm:
            return comm_quality
            
        # 提取数据速率矩阵
        data_rate_matrix = isl_comm['data_rate_bps']
        
        # 获取可见邻居
        sat_to_sat = visibility_matrices.get('satellite_to_satellite', None)
        if sat_to_sat is None or satellite_index >= len(sat_to_sat):
            return comm_quality
        
        visible_neighbors = np.where(sat_to_sat[satellite_index] > 0)[0]
        
        # 填充通信质量
        for i, neighbor_idx in enumerate(visible_neighbors[:self.max_visible_neighbors]):
            if (satellite_index < data_rate_matrix.shape[0] and 
                neighbor_idx < data_rate_matrix.shape[1]):
                # 归一化通信质量到[0, 1]
                # 数据速率从bps转换为Mbps，然后归一化
                quality_bps = data_rate_matrix[satellite_index, neighbor_idx]
                quality_mbps = quality_bps / 1e6  # 转换为Mbps
                comm_quality[i] = min(quality_mbps / 1000.0, 1.0)  # 假设最大1000 Mbps
        
        return comm_quality
    
    def _build_time_info(self, current_timeslot: int, max_timeslots: int) -> np.ndarray:
        """
        构建时间信息
        
        Returns:
            时间信息向量 [3]
        """
        time_info = np.zeros(3, dtype=np.float32)
        
        # 仿真进度
        time_info[0] = current_timeslot / max_timeslots
        
        # 剩余时隙比例
        time_info[1] = (max_timeslots - current_timeslot) / max_timeslots
        
        # 一天中的时间（归一化到[0, 1]）
        # 假设每个时隙3秒，一天86400秒
        seconds_in_day = (current_timeslot * 3) % 86400
        time_info[2] = seconds_in_day / 86400.0
        
        return time_info
    
    def get_observation_shape(self) -> Dict[str, Tuple]:
        """
        获取观测空间的形状
        
        Returns:
            各个观测组件的形状
        """
        return {
            'own_state': (self.state_feature_dim,),
            'task_queue': (self.max_queue_size, self.task_feature_dim),
            'task_mask': (self.max_queue_size,),
            'action_mask': (self.max_queue_size, self.num_action_targets),
            'neighbor_states': (self.max_visible_neighbors, self.neighbor_feature_dim),
            'comm_quality': (self.max_visible_neighbors,),
            'time_info': (3,)
        }
    
    def validate_observation(self, observation: Dict) -> bool:
        """
        验证观测是否符合预期格式
        
        Args:
            observation: 观测字典
        
        Returns:
            是否有效
        """
        expected_shapes = self.get_observation_shape()
        
        for key, expected_shape in expected_shapes.items():
            if key not in observation:
                logger.error(f"Missing observation key: {key}")
                return False
            
            actual_shape = observation[key].shape
            if actual_shape != expected_shape:
                logger.error(f"Shape mismatch for {key}: expected {expected_shape}, got {actual_shape}")
                return False
            
            # 检查数值范围
            if key != 'task_mask' and key != 'action_mask':
                if np.any(observation[key] < 0) or np.any(observation[key] > 1):
                    logger.warning(f"Values out of [0, 1] range in {key}")
        
        return True