"""
Training loop for MAPPO on SPACE3 environment
多智能体训练循环 - 模仿参考代码的run_PPO.py结构
"""

import datetime
import os
import sys
import numpy as np
from typing import Dict, Tuple, List

# 添加项目路径
curr_path = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(curr_path)))
sys.path.append(project_root)

from src.agent.mappo_trans.methods.transformer_ppo import TransformerPPO
from src.agent.mappo_trans.methods.buffer import MultiAgentBuffer
from src.agent.mappo_trans.config import MAPPOConfig, get_default_config
from src.agent.mappo_trans.utils import (
    extract_features_from_obs, aggregate_agent_rewards, 
    count_agent_tasks, calculate_system_utilization,
    log_metrics, Normalization
)

# 导入SPACE3环境
from src.env.space_env import make_env


def evaluate_policy(config: MAPPOConfig, env, agent: TransformerPPO, 
                   state_norm=None, n_episodes: int = 3) -> Dict[str, float]:
    """
    评估策略性能
    模仿参考代码的evaluate_policy函数
    
    Args:
        config: 配置参数
        env: 环境实例
        agent: MAPPO智能体
        state_norm: 状态归一化器
        n_episodes: 评估episodes数量
    
    Returns:
        评估指标字典
    """
    total_rewards = []
    total_completed = 0
    total_dropped = 0
    total_tasks = 0
    total_utilization = 0
    episode_lengths = []
    
    for episode in range(n_episodes):
        observations, infos = env.reset()
        
        # 状态归一化
        if state_norm is not None and config.use_state_norm:
            for agent_id in observations:
                obs_vector = extract_features_from_obs(observations[agent_id])
                obs_vector = state_norm(obs_vector, update=False)
                # 这里需要将归一化后的向量转回观测字典格式（简化省略）
        
        episode_reward = 0
        episode_completed = 0
        episode_dropped = 0
        episode_tasks = 0
        episode_utilization = 0
        step_count = 0
        
        done = False
        while not done and step_count < config.max_timeslots:
            step_count += 1
            
            # 使用确定性策略
            actions = agent.evaluate(observations)
            
            # 环境步进
            next_observations, rewards, terminations, truncations, infos = env.step(actions)
            
            # 统计信息
            reward_stats = aggregate_agent_rewards(rewards)
            episode_reward += reward_stats['total_reward']
            
            # 统计任务处理情况
            task_counts = count_agent_tasks(observations)
            episode_tasks += sum(task_counts.values())
            
            # 统计系统利用率
            utilization = calculate_system_utilization(observations)
            episode_utilization += utilization
            
            # 从info中获取完成和丢弃的任务数（如果环境提供）
            for agent_id, info in infos.items():
                episode_completed += info.get('tasks_completed', 0)
                episode_dropped += info.get('tasks_dropped', 0)
            
            # 检查终止条件
            done = any(terminations.values()) or any(truncations.values())
            observations = next_observations
        
        # 记录episode统计
        total_rewards.append(episode_reward)
        total_completed += episode_completed
        total_dropped += episode_dropped
        total_tasks += episode_tasks
        total_utilization += episode_utilization
        episode_lengths.append(step_count)
    
    # 计算平均指标
    avg_reward = np.mean(total_rewards)
    avg_completed = total_completed / n_episodes
    avg_dropped = total_dropped / n_episodes
    avg_tasks = total_tasks / n_episodes
    avg_utilization = total_utilization / (n_episodes * np.mean(episode_lengths))
    completion_rate = avg_completed / max(avg_tasks, 1)
    
    return {
        'avg_reward': avg_reward,
        'avg_completed': avg_completed,
        'avg_dropped': avg_dropped,
        'avg_tasks': avg_tasks,
        'completion_rate': completion_rate,
        'avg_utilization': avg_utilization,
        'avg_episode_length': np.mean(episode_lengths)
    }


def collect_rollout(env, agent: TransformerPPO, buffer: MultiAgentBuffer, 
                   config: MAPPOConfig, state_norm=None) -> Dict[str, float]:
    """
    收集一轮经验
    
    Args:
        env: 环境实例
        agent: MAPPO智能体
        buffer: 经验缓冲区
        config: 配置参数
        state_norm: 状态归一化器
    
    Returns:
        收集统计信息
    """
    observations, _ = env.reset()
    
    total_reward = 0
    total_completed = 0
    total_dropped = 0
    total_tasks = 0
    step_count = 0
    
    for step in range(config.rollout_length):
        step_count += 1
        
        # 状态归一化（如果启用）
        if state_norm is not None and config.use_state_norm:
            normalized_obs = {}
            for agent_id in observations:
                obs_vector = extract_features_from_obs(observations[agent_id])
                obs_vector = state_norm(obs_vector, update=True)
                normalized_obs[agent_id] = observations[agent_id]  # 简化：保持原格式
        else:
            normalized_obs = observations
        
        # 生成动作
        actions, log_probs, values = agent.act(normalized_obs)
        
        # 环境步进
        next_observations, rewards, terminations, truncations, infos = env.step(actions)
        
        # 转换观测为特征向量（用于存储）
        obs_vectors = {}
        for agent_id in observations:
            obs_vectors[agent_id] = extract_features_from_obs(observations[agent_id])
        
        # 存储经验
        dones = {agent_id: terminations[agent_id] or truncations[agent_id] 
                for agent_id in observations.keys()}
        
        buffer.store(
            observations=obs_vectors,
            actions=actions,
            log_probs=log_probs,
            rewards=rewards,
            values=values,
            dones=dones
        )
        
        # 统计信息
        reward_stats = aggregate_agent_rewards(rewards)
        total_reward += reward_stats['total_reward']
        
        task_counts = count_agent_tasks(observations)
        total_tasks += sum(task_counts.values())
        
        # 统计完成和丢弃任务
        for agent_id, info in infos.items():
            total_completed += info.get('tasks_completed', 0)
            total_dropped += info.get('tasks_dropped', 0)
        
        # 更新观测
        observations = next_observations
        
        # 检查是否提前终止
        if any(terminations.values()):
            break
    
    stats = {
        'total_reward': total_reward,
        'avg_reward': total_reward / step_count,
        'total_completed': total_completed,
        'total_dropped': total_dropped,
        'total_tasks': total_tasks,
        'completion_rate': total_completed / max(total_tasks, 1),
        'steps': step_count
    }
    
    return stats


def train_mappo(config: MAPPOConfig = None) -> Tuple[List[float], Dict[str, List]]:
    """
    主训练函数
    模仿参考代码的train函数结构
    
    Args:
        config: 配置参数
    
    Returns:
        训练统计信息
    """
    if config is None:
        config = get_default_config()
    
    start_time = datetime.datetime.now()
    print(f'Start MAPPO training on SPACE3!')
    print(f'Env: {config.env_name}, Algo: {config.algo_name}, Device: {config.device}')
    
    # 创建环境
    env = make_env()
    env_evaluate = make_env()  # 评估环境
    
    print(f"n_agents: {config.n_agents}, obs_dim: {config.obs_dim}, action_dim: {config.action_dim}")
    
    # 创建智能体和缓冲区
    agent = TransformerPPO(config)
    buffer = MultiAgentBuffer(config)
    
    # 归一化器（如果启用）
    state_norm = None
    if config.use_state_norm:
        state_norm = Normalization(shape=config.obs_dim)
    
    # 训练统计
    evaluate_num = 0
    evaluate_rewards = []
    training_rewards = []
    training_stats = {
        'policy_loss': [],
        'value_loss': [],
        'entropy': [],
        'completion_rate': [],
        'utilization': []
    }
    
    total_steps = 0
    n_iterations = config.total_timesteps // config.rollout_length
    
    print(f"Training for {n_iterations} iterations, {config.total_timesteps} total steps")
    
    # 主训练循环
    for iteration in range(n_iterations):
        # 收集经验
        rollout_stats = collect_rollout(env, agent, buffer, config, state_norm)
        total_steps += rollout_stats['steps']
        
        # 更新网络
        if buffer.is_ready():
            update_stats = agent.update(buffer)
            
            # 记录训练统计
            for key, value in update_stats.items():
                if key in training_stats:
                    training_stats[key].append(value)
            
            # 清空缓冲区
            buffer.clear()
        
        # 记录训练奖励
        training_rewards.append(rollout_stats['avg_reward'])
        training_stats['completion_rate'].append(rollout_stats['completion_rate'])
        
        # 时隙输出（核心功能）
        timeslot = iteration + 1
        metrics = {
            'Reward': rollout_stats['avg_reward'],
            'Complete': int(rollout_stats['total_completed']),
            'Drop': int(rollout_stats['total_dropped']),
            'Rate': rollout_stats['completion_rate'],
            'Total_Tasks': int(rollout_stats['total_tasks'])
        }
        
        # 添加数据源信息（仅在前几个时隙显示）
        if timeslot <= 5:
            print(f"\n[时隙 {timeslot} 数据源信息]")
            print("- 任务生成源: src/env/physics_layer/task_generator.py")
            print("- 地面站数据: src/env/env_data/global_ground_stations.csv (420个地面站)")
            print("- 卫星位置数据: src/env/env_data/satellite_data_72_0.csv (72颗LEO卫星)")
            print("- 云计算中心: src/env/env_data/cloud_station.csv (5个云中心)")
            print("- 任务生成基于地理位置特征和泊松分布，每个时隙生成约1000-2000个任务")
        
        log_metrics(timeslot, metrics, config)
        
        # 定期评估
        if iteration % config.eval_freq == 0:
            evaluate_num += 1
            eval_stats = evaluate_policy(config, env_evaluate, agent, state_norm)
            evaluate_rewards.append(eval_stats['avg_reward'])
            
            print(f"\n[Evaluation {evaluate_num}] "
                  f"Reward: {eval_stats['avg_reward']:.2f} | "
                  f"Completion: {eval_stats['completion_rate']:.2%} | "
                  f"Utilization: {eval_stats['avg_utilization']:.2%}")
        
        # 定期保存模型
        if iteration % config.save_freq == 0 and iteration > 0:
            model_path = config.get_model_path(f'mappo_iter_{iteration}.pt')
            agent.save(model_path)
            print(f"Model saved to {model_path}")
    
    # 训练结束
    end_time = datetime.datetime.now()
    training_time = (end_time - start_time).total_seconds()
    
    print(f"\nTraining completed!")
    print(f"Total time: {training_time:.1f}s")
    print(f"Total steps: {total_steps}")
    print(f"Final reward: {training_rewards[-1] if training_rewards else 0:.2f}")
    
    # 最终评估
    final_eval = evaluate_policy(config, env_evaluate, agent, state_norm, n_episodes=5)
    print(f"Final evaluation: {final_eval['avg_reward']:.2f} ± {np.std([final_eval['avg_reward']]):.2f}")
    
    # 保存最终模型
    final_model_path = config.get_model_path('mappo_final.pt')
    agent.save(final_model_path)
    
    return training_rewards, training_stats


def run_ppo() -> Tuple[List[float], float, float, float, MAPPOConfig]:
    """
    兼容参考代码的接口
    运行PPO训练并返回结果
    
    Returns:
        rewards: 训练奖励列表
        avg_completion_rate: 平均完成率
        avg_time: 平均时间
        avg_utilization: 平均利用率
        config: 配置参数
    """
    config = get_default_config()
    
    # 运行训练
    rewards, stats = train_mappo(config)
    
    # 计算统计指标
    avg_completion_rate = np.mean(stats['completion_rate']) if stats['completion_rate'] else 0.0
    avg_time = config.total_timesteps / len(rewards) if rewards else 0.0
    avg_utilization = 0.5  # 简化：固定值
    
    return rewards, avg_completion_rate, avg_time, avg_utilization, config


if __name__ == "__main__":
    # 直接运行训练
    config = get_default_config()
    rewards, stats = train_mappo(config)
    
    print(f"Training completed with {len(rewards)} iterations")
    if rewards:
        print(f"Final reward: {rewards[-1]:.2f}")
        print(f"Best reward: {max(rewards):.2f}")
        print(f"Average reward: {np.mean(rewards):.2f}")
    
    # 简单可视化
    if len(rewards) > 0:
        from src.agent.mappo_trans.utils import plot_rewards
        plot_rewards(rewards, config, tag="train")