# SPACE3 Environment Configuration for Transformer-MAPPO
# 专门为序列动作强化学习优化的配置

# 任务队列和padding配置
max_queue_size: 20              # 最大任务队列长度（用于padding）
task_feature_dim: 8              # 任务特征维度
state_feature_dim: 12            # 卫星状态特征维度
neighbor_feature_dim: 5          # 邻居状态特征维度

# 动作空间配置
max_visible_neighbors: 10        # 最大可见邻居数
num_cloud_targets: 5             # 云中心目标数
max_target_load: 50              # 单个目标最大负载

# 奖励权重配置
reward_weights:
  completion: 10.0               # 任务完成奖励
  drop: -5.0                     # 任务丢弃惩罚
  delay: -0.01                   # 延迟惩罚系数
  energy: -0.001                 # 能耗惩罚系数
  load_balance: 1.0              # 负载均衡奖励
  invalid_action: -1.0           # 无效动作惩罚
  cooperation: 2.0               # 协作奖励

# 归一化参数
normalization:
  max_tasks_per_step: 100        # 每步最大任务数
  max_delay: 1000.0              # 最大延迟（秒）
  max_energy: 10000.0            # 最大能耗（焦耳）
  target_load_variance: 0.1      # 目标负载方差

# 观测空间归一化参数
observation_normalization:
  task:
    data_size_max: 100.0         # 最大数据大小（MB）
    complexity_max: 1000.0       # 最大复杂度（cycles/bit）
    priority_max: 5.0            # 最大优先级
    deadline_max: 3600.0         # 最大截止时间（秒）
    lat_max: 90.0                # 最大纬度
    lon_max: 180.0               # 最大经度
  state:
    cpu_max: 100.0               # 最大CPU使用率（%）
    memory_max: 100.0            # 最大内存使用率（%）
    battery_max: 100.0           # 最大电池电量（%）
    tasks_max: 1000.0            # 最大任务数
    energy_max: 10000.0          # 最大能耗（J）
  neighbor:
    distance_max: 5500.0         # 最大距离（km）
    comm_rate_max: 1000.0        # 最大通信速率（Mbps）
    load_max: 100.0              # 最大负载（%）

# 训练相关配置
training:
  batch_size: 32                 # 批大小
  learning_rate: 3e-4            # 学习率
  gamma: 0.99                    # 折扣因子
  gae_lambda: 0.95               # GAE lambda
  clip_epsilon: 0.2              # PPO clip参数
  value_loss_coef: 0.5           # 价值损失系数
  entropy_coef: 0.01             # 熵正则化系数
  max_grad_norm: 0.5             # 梯度裁剪
  
# 环境运行配置
environment:
  render_mode: null              # 渲染模式（human/rgb_array/null）
  seed: 42                       # 随机种子
  max_episode_steps: 2000        # 最大episode步数
  
# 日志配置
logging:
  level: INFO                    # 日志级别
  save_frequency: 100            # 保存频率（步）
  log_dir: logs/space_env         # 日志目录

# 数据路径配置
data_paths:
  ground_stations: "src/env/env_data/global_ground_stations.csv"
  satellite_data: "src/env/env_data/satellite_data72_1.csv"
  cloud_centers: "src/env/env_data/cloud_centers.csv"
  base_config: "src/env/physics_layer/config.yaml"