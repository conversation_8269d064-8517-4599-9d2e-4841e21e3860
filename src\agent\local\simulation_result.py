"""
Simulation result data models for local computation
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import numpy as np

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus


@dataclass
class TimeslotResult:
    """Single timeslot result"""
    timeslot: int
    tasks_generated: int
    tasks_assigned: int
    tasks_completed: List[ComputeTask] = field(default_factory=list)
    tasks_dropped: List[ComputeTask] = field(default_factory=list)
    tasks_in_progress: int = 0
    satellite_energy_states: Dict[int, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timeslot': self.timeslot,
            'tasks_generated': self.tasks_generated,
            'tasks_assigned': self.tasks_assigned,
            'tasks_completed': len(self.tasks_completed),
            'tasks_dropped': len(self.tasks_dropped),
            'tasks_in_progress': self.tasks_in_progress,
            'avg_battery_level': np.mean(list(self.satellite_energy_states.values())) if self.satellite_energy_states else 0
        }


@dataclass
class PerformanceMetrics:
    """Detailed performance metrics"""
    # Delay metrics (seconds)
    mean_delay: float = 0.0
    median_delay: float = 0.0
    p90_delay: float = 0.0
    p99_delay: float = 0.0
    max_delay: float = 0.0
    min_delay: float = 0.0
    
    # Energy metrics (Joules)
    total_energy_consumed: float = 0.0
    energy_per_completed_task: float = 0.0
    energy_efficiency: float = 0.0  # tasks completed per KJ
    
    # Completion rate metrics
    type1_completion_rate: float = 0.0  # REALTIME
    type2_completion_rate: float = 0.0  # NORMAL  
    type3_completion_rate: float = 0.0  # COMPUTE_INTENSIVE
    
    # Priority-based completion rates
    high_priority_completion: float = 0.0  # Priority 8-10
    medium_priority_completion: float = 0.0  # Priority 4-7
    low_priority_completion: float = 0.0  # Priority 1-3
    
    # Weighted completion rate
    priority_weighted_completion: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for export"""
        return {
            'delay_metrics': {
                'mean': self.mean_delay,
                'median': self.median_delay,
                'p90': self.p90_delay,
                'p99': self.p99_delay,
                'max': self.max_delay,
                'min': self.min_delay
            },
            'energy_metrics': {
                'total_consumed': self.total_energy_consumed,
                'per_task': self.energy_per_completed_task,
                'efficiency': self.energy_efficiency
            },
            'completion_by_type': {
                'REALTIME': self.type1_completion_rate,
                'NORMAL': self.type2_completion_rate,
                'COMPUTE_INTENSIVE': self.type3_completion_rate
            },
            'completion_by_priority': {
                'high': self.high_priority_completion,
                'medium': self.medium_priority_completion,
                'low': self.low_priority_completion,
                'weighted': self.priority_weighted_completion
            }
        }


@dataclass
class SimulationResult:
    """Complete simulation result"""
    # Summary statistics
    total_tasks_generated: int = 0
    total_tasks_completed: int = 0
    total_tasks_dropped: int = 0
    overall_completion_rate: float = 0.0
    
    # Average metrics
    avg_delay_per_task: float = 0.0  # seconds
    avg_energy_per_task: float = 0.0  # Joules
    
    # Completion rates by priority (Dict[priority, rate])
    completion_rate_by_priority: Dict[int, float] = field(default_factory=dict)
    
    # Detailed metrics
    performance_metrics: Optional[PerformanceMetrics] = None
    
    # Timeslot results for analysis
    timeslot_results: List[TimeslotResult] = field(default_factory=list)
    
    # Satellite-level statistics
    satellite_statistics: Dict[int, Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for export"""
        # Convert numpy int64 to regular int for JSON serialization
        completion_by_priority_converted = {}
        for k, v in self.completion_rate_by_priority.items():
            completion_by_priority_converted[str(k)] = float(v)
        
        # Convert satellite statistics (might contain numpy types)
        satellite_stats_converted = {}
        if self.satellite_statistics:
            for sat_id, stats in self.satellite_statistics.items():
                satellite_stats_converted[str(sat_id)] = {
                    'tasks_processed': int(stats['tasks_processed']),
                    'tasks_dropped': int(stats['tasks_dropped']),
                    'energy_consumed': float(stats['energy_consumed']),
                    'final_battery': float(stats['final_battery']),
                    'utilization': float(stats['utilization'])
                }
        
        return {
            'summary': {
                'total_generated': int(self.total_tasks_generated),
                'total_completed': int(self.total_tasks_completed),
                'total_dropped': int(self.total_tasks_dropped),
                'overall_completion_rate': float(self.overall_completion_rate),
                'avg_delay': float(self.avg_delay_per_task),
                'avg_energy': float(self.avg_energy_per_task)
            },
            'completion_by_priority': completion_by_priority_converted,
            'performance_metrics': self.performance_metrics.to_dict() if self.performance_metrics else None,
            'satellite_stats': satellite_stats_converted
        }
    
    def print_summary(self):
        """Print formatted summary of results"""
        print("\n" + "="*60)
        print(" SPACE3 Local Computation Simulation Results")
        print("="*60)
        
        print(f"\nTask Statistics:")
        print(f"  Total Generated: {self.total_tasks_generated:,}")
        print(f"  Total Completed: {self.total_tasks_completed:,} ({self.overall_completion_rate:.1%})")
        print(f"  Total Dropped:   {self.total_tasks_dropped:,} ({self.total_tasks_dropped/max(1, self.total_tasks_generated):.1%})")
        
        print(f"\nPerformance Metrics:")
        print(f"  Average Delay: {self.avg_delay_per_task:.2f} seconds")
        print(f"  Average Energy: {self.avg_energy_per_task:.2f} Joules")
        
        if self.performance_metrics:
            print(f"\nDelay Distribution:")
            print(f"  Mean:   {self.performance_metrics.mean_delay:.2f}s")
            print(f"  Median: {self.performance_metrics.median_delay:.2f}s")
            print(f"  P90:    {self.performance_metrics.p90_delay:.2f}s")
            print(f"  P99:    {self.performance_metrics.p99_delay:.2f}s")
            
            print(f"\nCompletion Rates by Type:")
            print(f"  REALTIME:          {self.performance_metrics.type1_completion_rate:.1%}")
            print(f"  NORMAL:            {self.performance_metrics.type2_completion_rate:.1%}")
            print(f"  COMPUTE_INTENSIVE: {self.performance_metrics.type3_completion_rate:.1%}")
            
            print(f"\nCompletion Rates by Priority:")
            print(f"  High (1):      {self.performance_metrics.high_priority_completion:.1%}")
            print(f"  Medium (2):    {self.performance_metrics.medium_priority_completion:.1%}")
            print(f"  Low (3):       {self.performance_metrics.low_priority_completion:.1%}")
        
        print("\n" + "="*60)