"""
Local compute simulator for SPACE3
Implements baseline local-only task processing without offloading
"""

import yaml
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging
from datetime import datetime
import json

# Absolute imports as per coding standard
from src.env.Foundation_Layer.time_manager import TimeManager, create_time_manager_from_config
from src.env.Foundation_Layer.logging_config import get_logger
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_distributor import TaskDistributor
from src.env.physics_layer.task_models import Task, TaskType, AssignmentStatus
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus, ProcessingResult
from src.agent.local.simulation_result import SimulationResult, PerformanceMetrics, TimeslotResult


class LocalComputeSimulator:
    """
    Local computation simulator for SPACE3
    All tasks are processed locally on receiving satellites without offloading
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize local compute simulator
        
        Args:
            config_path: Path to configuration file (defaults to physics_layer/config.yaml)
        """
        # Setup configuration path
        if config_path is None:
            config_path = str(Path(__file__).parent.parent.parent / 'env' / 'physics_layer' / 'config.yaml')
        self.config_path = config_path
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize logger
        self.logger = get_logger(__name__)
        
        # System parameters from config
        self.num_satellites = self.config['system']['num_leo_satellites']
        self.num_users = self.config['system']['num_users']
        self.timeslot_duration = self.config['system']['timeslot_duration_s']
        self.total_timeslots = self.config['system']['total_timeslots']
        
        # Initialize components
        self.time_manager = None
        self.orbital_updater = None
        self.task_generator = None
        self.task_distributor = None
        self.satellites = []
        
        # Results storage
        self.timeslot_results = []
        self.all_completed_tasks = []
        self.all_dropped_tasks = []
        
        # Global task mapping for statistics tracking
        self.all_tasks_map = {}  # task_id -> original Task object
        
        # Statistics tracking
        self.task_stats_by_type = {
            TaskType.REALTIME: {'generated': 0, 'completed': 0},
            TaskType.NORMAL: {'generated': 0, 'completed': 0},
            TaskType.COMPUTE_INTENSIVE: {'generated': 0, 'completed': 0}
        }
        
        self.task_stats_by_priority = {i: {'generated': 0, 'completed': 0} for i in range(1, 11)}
        
        self.logger.info(f"LocalComputeSimulator initialized with {self.num_satellites} satellites")
        
    @handle_errors(module="local_compute", function="load_config")
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from yaml file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise SpaceSimulationError(
                message=f"Failed to load config file: {self.config_path}",
                error_code="CONFIG_LOAD_ERROR"
            )
    
    @handle_errors(module="local_compute", function="initialize_components")
    def _initialize_components(self):
        """Initialize all simulation components"""
        self.logger.info("Initializing simulation components...")
        
        # Initialize time manager
        self.time_manager = create_time_manager_from_config(self.config)
        
        # Initialize orbital updater
        self.orbital_updater = OrbitalUpdater(config_file=self.config_path)
        
        # Initialize task generator
        self.task_generator = TaskGenerator(config_file=self.config_path)
        # Load ground station data
        ground_station_file = str(Path(__file__).parent.parent.parent / 'env' / 'env_data' / 'global_ground_stations.csv')
        self.task_generator.load_locations_from_csv(ground_station_file)
        
        # Initialize task distributor
        self.task_distributor = TaskDistributor(
            orbital_updater=self.orbital_updater,
            config_file=self.config_path,
            time_manager=self.time_manager
        )
        
        # Initialize satellites using actual satellite IDs from orbital data
        self.satellites = {}  # Use dictionary with satellite ID as key
        
        # Get satellite IDs from first timeslot to know actual IDs
        satellites_at_t0 = self.orbital_updater.get_satellites_at_time(0)
        
        for sat_id_str in satellites_at_t0.keys():
            sat_id = int(sat_id_str)
            satellite = SatelliteCompute(
                satellite_id=sat_id,
                config=self.config,
                orbital_updater=self.orbital_updater
            )
            self.satellites[sat_id] = satellite
        
        self.logger.info(f"Initialized {len(self.satellites)} satellites with IDs: {sorted(self.satellites.keys())}")
        
    def _check_satellite_illumination(self, satellite_id: int, timeslot: int) -> bool:
        """
        Check if satellite is illuminated (in sunlight)
        
        Args:
            satellite_id: Actual satellite ID (e.g., 111, 112, etc.)
            timeslot: Current timeslot
            
        Returns:
            True if illuminated, False otherwise
        """
        # Get satellite data at timeslot
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        
        sat_key = str(satellite_id)
        
        if sat_key in satellites:
            return satellites[sat_key].illuminated
        
        # Default to illuminated if not found
        return True
    
    @handle_errors(module="local_compute", function="process_timeslot")
    def _process_single_timeslot(self, timeslot: int) -> TimeslotResult:
        """
        Process a single timeslot
        
        Args:
            timeslot: Current timeslot number
            
        Returns:
            TimeslotResult with processing results
        """
        # Create timeslot result
        result = TimeslotResult(timeslot=timeslot, tasks_generated=0, tasks_assigned=0)
        
        # Get time context
        time_context = self.time_manager.get_time_context(timeslot)
        current_time = time_context.simulation_time
        
        # 1. Generate tasks for this timeslot
        tasks_by_location = self.task_generator.generate_tasks_for_timeslot(timeslot)
        
        # Flatten tasks and count statistics
        all_tasks = []
        for location_id, tasks in tasks_by_location.items():
            for task in tasks:
                all_tasks.append(task)
                # Store in global mapping for later statistics
                self.all_tasks_map[task.task_id] = task
                # Update statistics
                self.task_stats_by_type[task.type_id]['generated'] += 1
                self.task_stats_by_priority[task.priority]['generated'] += 1
        
        result.tasks_generated = len(all_tasks)
        
        # 2. Distribute tasks to satellites based on visibility
        if all_tasks:
            assignments = self.task_distributor.distribute_tasks(all_tasks, timeslot)
            
            # 3. Add successfully assigned tasks to satellite queues
            for assignment in assignments:
                if assignment.status == AssignmentStatus.ASSIGNED:
                    satellite_id = assignment.assigned_satellite_id
                    if satellite_id in self.satellites:
                        # Set arrival time for the task
                        assignment.task.arrival_time = current_time
                        # Use the proper add_task method which handles conversion and initialization
                        success = self.satellites[satellite_id].add_task(assignment.task)
                        if success:
                            result.tasks_assigned += 1
                            self.logger.debug(f"Task {assignment.task.task_id} added to satellite {satellite_id} queue")
                        else:
                            self.logger.debug(f"Task {assignment.task.task_id} rejected by satellite {satellite_id}")
        
        # 4. Process tasks on each satellite
        for sat_id, satellite in self.satellites.items():
            # Check illumination
            illuminated = self._check_satellite_illumination(sat_id, timeslot)
            
            # Update current time for satellite
            satellite.processing_start_time = current_time
            # Also update the current time used for scheduling
            satellite.current_time = current_time
            
            # Set CPU allocation strategy (local algorithm uses uniform)
            satellite.set_cpu_allocation_strategy('uniform')
            
            # Process timeslot
            processing_result = satellite.process_timeslot(
                duration=self.timeslot_duration,
                illuminated=illuminated
            )
            
            # Collect completed tasks
            for task in processing_result.completed_tasks:
                # Environment has already calculated all delays and energy
                result.tasks_completed.append(task)
                self.all_completed_tasks.append(task)
                
                # Update statistics using global task mapping
                if task.task_id in self.all_tasks_map:
                    orig_task = self.all_tasks_map[task.task_id]
                    self.task_stats_by_type[orig_task.type_id]['completed'] += 1
                    self.task_stats_by_priority[orig_task.priority]['completed'] += 1
                else:
                    self.logger.warning(f"Task {task.task_id} not found in global mapping")
            
            # Collect dropped tasks
            for task in processing_result.dropped_tasks:
                result.tasks_dropped.append(task)
                self.all_dropped_tasks.append(task)
            
            # Record satellite energy state
            result.satellite_energy_states[sat_id] = satellite.battery_energy
        
        # Count tasks still in progress
        result.tasks_in_progress = sum(len(sat.task_queue) for sat in self.satellites.values())
        
        return result
    
    def _calculate_performance_metrics(self) -> PerformanceMetrics:
        """
        Calculate detailed performance metrics
        
        Returns:
            PerformanceMetrics object with calculated metrics
        """
        metrics = PerformanceMetrics()
        
        # Calculate delay metrics
        if self.all_completed_tasks:
            delays = []
            for task in self.all_completed_tasks:
                # Use actual total delay if available
                if hasattr(task, 'actual_total_delay') and task.actual_total_delay > 0:
                    delays.append(task.actual_total_delay)
                elif task.completion_time and task.arrival_time:
                    # Fallback to timeslot-based calculation
                    delay = task.completion_time - task.arrival_time
                    delays.append(delay)
            
            if delays:
                delays = np.array(delays)
                metrics.mean_delay = np.mean(delays)
                metrics.median_delay = np.median(delays)
                metrics.p90_delay = np.percentile(delays, 90)
                metrics.p99_delay = np.percentile(delays, 99)
                metrics.max_delay = np.max(delays)
                metrics.min_delay = np.min(delays)
        
        # Calculate energy metrics
        total_energy = sum(sat.total_energy_consumed for sat in self.satellites.values())
        metrics.total_energy_consumed = total_energy
        
        if self.all_completed_tasks:
            metrics.energy_per_completed_task = total_energy / len(self.all_completed_tasks)
            metrics.energy_efficiency = len(self.all_completed_tasks) / (total_energy / 1000) if total_energy > 0 else 0
        
        # Calculate completion rates by type
        for task_type, stats in self.task_stats_by_type.items():
            if stats['generated'] > 0:
                completion_rate = stats['completed'] / stats['generated']
                if task_type == TaskType.REALTIME:
                    metrics.type1_completion_rate = completion_rate
                elif task_type == TaskType.NORMAL:
                    metrics.type2_completion_rate = completion_rate
                elif task_type == TaskType.COMPUTE_INTENSIVE:
                    metrics.type3_completion_rate = completion_rate
        
        # Calculate completion rates by priority (1=high, 2=medium, 3=low)
        high_gen = self.task_stats_by_priority[1]['generated']
        high_comp = self.task_stats_by_priority[1]['completed']
        metrics.high_priority_completion = high_comp / high_gen if high_gen > 0 else 0
        
        med_gen = self.task_stats_by_priority[2]['generated']
        med_comp = self.task_stats_by_priority[2]['completed']
        metrics.medium_priority_completion = med_comp / med_gen if med_gen > 0 else 0
        
        low_gen = self.task_stats_by_priority[3]['generated']
        low_comp = self.task_stats_by_priority[3]['completed']
        metrics.low_priority_completion = low_comp / low_gen if low_gen > 0 else 0
        
        # Calculate weighted completion rate (1=high weight 0.5, 2=medium weight 0.3, 3=low weight 0.2)
        weights = {1: 0.5, 2: 0.3, 3: 0.2}
        weighted_sum = 0
        for priority, weight in weights.items():
            gen = self.task_stats_by_priority[priority]['generated']
            comp = self.task_stats_by_priority[priority]['completed']
            if gen > 0:
                weighted_sum += weight * (comp / gen)
        metrics.priority_weighted_completion = weighted_sum
        
        return metrics
    
    @handle_errors(module="local_compute", function="run_simulation")
    def run_simulation(self, total_timeslots: Optional[int] = None) -> SimulationResult:
        """
        Run complete simulation
        
        Args:
            total_timeslots: Number of timeslots to simulate (defaults to config value)
            
        Returns:
            SimulationResult with complete results
        """
        if total_timeslots is None:
            total_timeslots = self.total_timeslots
        
        self.logger.info(f"Starting simulation for {total_timeslots} timeslots")
        
        # Initialize components
        self._initialize_components()
        
        # Reset statistics
        self.timeslot_results = []
        self.all_completed_tasks = []
        self.all_dropped_tasks = []
        self.all_tasks_map = {}  # Clear task mapping
        
        # Run simulation
        for timeslot in range(total_timeslots):
            if timeslot % 100 == 0:
                self.logger.info(f"Processing timeslot {timeslot}/{total_timeslots}")
            
            # Process single timeslot
            result = self._process_single_timeslot(timeslot)
            self.timeslot_results.append(result)
        
        # Calculate final statistics
        total_generated = sum(r.tasks_generated for r in self.timeslot_results)
        total_completed = len(self.all_completed_tasks)
        total_dropped = len(self.all_dropped_tasks)
        
        # Calculate average metrics
        avg_delay = 0.0
        avg_energy = 0.0
        
        if self.all_completed_tasks:
            delays = []
            energies = []
            for task in self.all_completed_tasks:
                # Use actual total delay if available
                if hasattr(task, 'actual_total_delay') and task.actual_total_delay > 0:
                    delays.append(task.actual_total_delay)
                elif task.completion_time and task.arrival_time:
                    # Fallback to timeslot-based calculation
                    delays.append(task.completion_time - task.arrival_time)
                
                # Use total energy (including transmission)
                if task.energy_consumed > 0:
                    energies.append(task.energy_consumed)
                else:
                    # Fallback: calculate based on complexity if not set
                    task_energy = task.complexity * 1.0e-10  # zeta_leo
                    energies.append(task_energy)
            
            if delays:
                avg_delay = np.mean(delays)
            if energies:
                avg_energy = np.mean(energies)
        
        # Calculate completion rates by priority
        completion_by_priority = {}
        for priority in range(1, 11):
            gen = self.task_stats_by_priority[priority]['generated']
            comp = self.task_stats_by_priority[priority]['completed']
            completion_by_priority[priority] = comp / gen if gen > 0 else 0
        
        # Calculate satellite statistics
        satellite_stats = {}
        for sat_id, satellite in self.satellites.items():
            satellite_stats[sat_id] = {
                'tasks_processed': satellite.total_tasks_processed,
                'tasks_dropped': satellite.total_tasks_dropped,
                'energy_consumed': satellite.total_energy_consumed,
                'final_battery': satellite.battery_energy,
                'utilization': satellite.total_tasks_processed / max(1, total_timeslots)
            }
        
        # Create simulation result
        result = SimulationResult(
            total_tasks_generated=total_generated,
            total_tasks_completed=total_completed,
            total_tasks_dropped=total_dropped,
            overall_completion_rate=total_completed / max(1, total_generated),
            avg_delay_per_task=avg_delay,
            avg_energy_per_task=avg_energy,
            completion_rate_by_priority=completion_by_priority,
            performance_metrics=self._calculate_performance_metrics(),
            timeslot_results=self.timeslot_results,
            satellite_statistics=satellite_stats
        )
        
        self.logger.info(f"Simulation completed: {total_completed}/{total_generated} tasks completed")
        
        return result
    
    def export_results(self, result: SimulationResult, output_path: str):
        """
        Export simulation results to file
        
        Args:
            result: SimulationResult to export
            output_path: Output file path (JSON format)
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, indent=2)
            self.logger.info(f"Results exported to {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to export results: {e}")
            raise