#!/usr/bin/env python3
"""
测试修复后的观测构建器
验证通信矩阵接口修复是否成功
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_observation_builder():
    """测试观测构建器修复"""
    print("="*60)
    print("测试ObservationBuilder通信矩阵接口修复")
    print("="*60)
    
    try:
        # 1. 初始化环境
        print("1. 初始化环境...")
        from src.env.space_env import make_env
        env = make_env()
        
        # 2. 重置环境（这会触发观测构建）
        print("2. 重置环境，测试观测构建...")
        observations, infos = env.reset()
        
        print(f"   成功重置环境！智能体数量: {len(observations)}")
        
        # 3. 检查观测结构
        print("3. 检查观测结构...")
        sample_agent = list(observations.keys())[0]
        sample_obs = observations[sample_agent]
        
        print(f"   样本智能体: {sample_agent}")
        print(f"   观测键: {list(sample_obs.keys())}")
        
        # 4. 检查通信质量字段
        if 'comm_quality' in sample_obs:
            comm_quality = sample_obs['comm_quality']
            print(f"   通信质量形状: {comm_quality.shape}")
            print(f"   通信质量范围: [{comm_quality.min():.3f}, {comm_quality.max():.3f}]")
            print(f"   非零通信质量数: {np.count_nonzero(comm_quality)}")
        else:
            print("   警告: 观测中缺少通信质量字段")
            
        # 5. 测试执行一步
        print("4. 测试执行一步...")
        actions = {}
        for agent_id in observations.keys():
            # 生成随机动作
            actions[agent_id] = np.zeros(env.max_queue_size, dtype=int)
        
        obs, rewards, terms, truncs, infos = env.step(actions)
        print(f"   成功执行一步！奖励数量: {len(rewards)}")
        
        return True
        
    except Exception as e:
        print(f"   错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_communication_data_extraction():
    """测试通信数据提取"""
    print("\n" + "="*60)
    print("测试通信数据提取逻辑")
    print("="*60)
    
    try:
        from src.env.physics_layer.communication_refactored import CommunicationManager
        from src.env.physics_layer.orbital import OrbitalUpdater
        
        # 初始化通信管理器
        comm_manager = CommunicationManager()
        
        # 获取通信矩阵
        isl_comm = comm_manager.get_isl_communication_matrix(1)
        
        print("1. ISL通信矩阵结构:")
        for key, value in isl_comm.items():
            if isinstance(value, np.ndarray):
                print(f"   {key}: {value.shape}, 范围: [{value.min():.2e}, {value.max():.2e}]")
            else:
                print(f"   {key}: {value}")
        
        # 测试数据提取
        data_rate_matrix = isl_comm['data_rate_bps']
        print(f"\n2. 数据速率矩阵提取成功: {data_rate_matrix.shape}")
        print(f"   非零元素数: {np.count_nonzero(data_rate_matrix)}")
        print(f"   最大数据速率: {data_rate_matrix.max() / 1e9:.2f} Gbps")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("观测构建器修复验证测试")
    print("="*60)
    
    # 运行测试
    test1 = test_communication_data_extraction()
    test2 = test_observation_builder()
    
    print("\n" + "="*60)
    print("测试结果总结:")
    print("="*60)
    print(f"通信数据提取: {'PASS' if test1 else 'FAIL'}")
    print(f"观测构建器: {'PASS' if test2 else 'FAIL'}")
    
    if test1 and test2:
        print("\n✅ 所有测试通过！ObservationBuilder接口修复成功")
        print("现在可以运行MAPPO训练了")
    else:
        print("\n❌ 存在失败的测试，需要进一步调试")