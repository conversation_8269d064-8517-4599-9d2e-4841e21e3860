"""
Transformer-based MAPPO implementation
基于Transformer的MAPPO算法 - 模仿参考代码的PPO_discrete结构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.distributions import Categorical
from torch.utils.data.sampler import BatchSampler, SubsetRandomSampler
from typing import Dict, <PERSON><PERSON>, List

from src.agent.mappo_trans.config import MAPPOConfig
from src.agent.mappo_trans.utils import orthogonal_init, apply_action_mask, sample_action_sequence


# ==================== Transformer策略网络 ====================

class TransformerActor(nn.Module):
    """
    基于Transformer的策略网络
    处理任务序列并生成动作序列
    """
    
    def __init__(self, config: MAPPOConfig):
        super(TransformerActor, self).__init__()
        self.config = config
        self.task_dim = config.task_feature_dim  # 8
        self.embed_dim = config.embed_dim  # 128
        self.hidden_dim = config.hidden_dim  # 128
        self.n_heads = config.n_heads  # 4
        self.action_dim = config.action_dim  # 16
        self.seq_len = config.sequence_length  # 20
        
        # 任务特征嵌入
        self.task_embedding = nn.Linear(self.task_dim, self.embed_dim)
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, self.seq_len, self.embed_dim))
        
        # 简化的Transformer编码器（单层）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.embed_dim,
            nhead=self.n_heads,
            dim_feedforward=self.hidden_dim,
            dropout=config.dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=config.n_layers)
        
        # 动作头
        self.action_head = nn.Linear(self.embed_dim, self.action_dim)
        
        # 激活函数
        self.activate_func = [nn.ReLU(), nn.Tanh()][config.use_tanh]
        
        # 初始化
        if config.use_orthogonal_init:
            self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        orthogonal_init(self.task_embedding)
        orthogonal_init(self.action_head, gain=0.01)
    
    def forward(self, task_queue: torch.Tensor, task_mask: torch.Tensor, 
                action_mask: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            task_queue: [batch_size, seq_len, task_dim]
            task_mask: [batch_size, seq_len] (1表示真实任务，0表示padding)
            action_mask: [batch_size, seq_len, action_dim] (1表示可行动作)
        
        Returns:
            action_logits: [batch_size, seq_len, action_dim]
        """
        batch_size, seq_len = task_queue.shape[:2]
        
        # 任务嵌入
        embedded = self.task_embedding(task_queue)  # [batch_size, seq_len, embed_dim]
        
        # 添加位置编码
        embedded = embedded + self.pos_encoding[:, :seq_len, :]
        
        # 创建注意力掩码（padding位置不参与注意力）
        # TransformerEncoder期望的mask格式：True表示需要忽略的位置
        attn_mask = (task_mask == 0)  # [batch_size, seq_len]
        
        # Transformer编码
        if attn_mask.any():
            # 如果有padding，使用mask
            encoded = self.transformer(embedded, src_key_padding_mask=attn_mask)
        else:
            # 如果没有padding，不使用mask
            encoded = self.transformer(embedded)
        
        # 生成动作logits
        action_logits = self.action_head(encoded)  # [batch_size, seq_len, action_dim]
        
        # 应用动作掩码
        masked_logits = apply_action_mask(action_logits, action_mask)
        
        return masked_logits
    
    def get_action(self, obs: Dict[str, np.ndarray], deterministic: bool = False) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成动作（单个智能体）
        
        Args:
            obs: 单个智能体的观测字典
            deterministic: 是否使用确定性策略
        
        Returns:
            actions: 动作序列 [seq_len]
            log_probs: 对数概率 [seq_len]
        """
        # 提取序列信息
        task_queue = torch.FloatTensor(obs['task_queue']).unsqueeze(0)  # [1, seq_len, task_dim]
        task_mask = torch.FloatTensor(obs['task_mask']).unsqueeze(0)    # [1, seq_len]
        action_mask = torch.FloatTensor(obs['action_mask']).unsqueeze(0)  # [1, seq_len, action_dim]
        
        with torch.no_grad():
            # 前向传播
            logits = self.forward(task_queue, task_mask, action_mask)  # [1, seq_len, action_dim]
            
            # 创建分布
            dist = Categorical(logits=logits)
            
            if deterministic:
                # 确定性动作：选择概率最大的动作
                actions = torch.argmax(logits, dim=-1)  # [1, seq_len]
                log_probs = dist.log_prob(actions)      # [1, seq_len]
            else:
                # 随机采样
                actions, log_probs = sample_action_sequence(dist, task_mask)
        
        return actions.squeeze(0).numpy(), log_probs.squeeze(0).numpy()


# ==================== 集中式批评家 ====================

class CentralizedCritic(nn.Module):
    """
    集中式批评家
    使用全局信息评估状态价值
    """
    
    def __init__(self, config: MAPPOConfig):
        super(CentralizedCritic, self).__init__()
        
        # 计算全局状态维度
        # 简化：每个智能体贡献 12(own_state) + 1(task_count) + 8(avg_task_features) = 21
        self.global_state_dim = config.n_agents * 21
        self.hidden_dim = config.hidden_dim
        
        # MLP网络
        self.fc1 = nn.Linear(self.global_state_dim, self.hidden_dim * 2)
        self.fc2 = nn.Linear(self.hidden_dim * 2, self.hidden_dim)
        self.fc3 = nn.Linear(self.hidden_dim, 1)
        
        self.activate_func = [nn.ReLU(), nn.Tanh()][config.use_tanh]
        
        # 初始化
        if config.use_orthogonal_init:
            orthogonal_init(self.fc1)
            orthogonal_init(self.fc2)
            orthogonal_init(self.fc3)
    
    def forward(self, global_state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            global_state: [batch_size, global_state_dim]
        
        Returns:
            values: [batch_size, 1]
        """
        x = self.activate_func(self.fc1(global_state))
        x = self.activate_func(self.fc2(x))
        values = self.fc3(x)
        
        return values
    
    def get_value(self, observations: Dict[str, Dict]) -> Dict[str, float]:
        """
        为所有智能体计算价值
        
        Args:
            observations: 所有智能体的观测字典
        
        Returns:
            values: 每个智能体的价值
        """
        from src.agent.mappo_trans.utils import build_global_state
        
        # 构建全局状态
        global_state = build_global_state(observations)
        global_state_tensor = torch.FloatTensor(global_state).unsqueeze(0)
        
        with torch.no_grad():
            value = self.forward(global_state_tensor).item()
        
        # 所有智能体共享同一个价值（简化实现）
        values = {agent_id: value for agent_id in observations.keys()}
        
        return values


# ==================== MAPPO主算法类 ====================

class TransformerPPO:
    """
    基于Transformer的MAPPO算法
    模仿参考代码PPO_discrete的接口结构
    """
    
    def __init__(self, config: MAPPOConfig):
        self.config = config
        
        # 算法参数
        self.lr_a = config.lr_a
        self.lr_c = config.lr_c
        self.gamma = config.gamma
        self.lamda = config.lamda
        self.epsilon = config.epsilon
        self.K_epochs = config.K_epochs
        self.entropy_coef = config.entropy_coef
        self.value_coef = config.value_coef
        self.max_grad_norm = config.max_grad_norm
        
        # 技巧开关
        self.use_grad_clip = config.use_grad_clip
        self.use_adv_norm = config.use_adv_norm
        
        # 网络
        self.actor = TransformerActor(config)
        self.critic = CentralizedCritic(config)
        
        # 优化器
        if config.set_adam_eps:
            self.optimizer_actor = torch.optim.Adam(self.actor.parameters(), lr=self.lr_a, eps=1e-5)
            self.optimizer_critic = torch.optim.Adam(self.critic.parameters(), lr=self.lr_c, eps=1e-5)
        else:
            self.optimizer_actor = torch.optim.Adam(self.actor.parameters(), lr=self.lr_a)
            self.optimizer_critic = torch.optim.Adam(self.critic.parameters(), lr=self.lr_c)
    
    def act(self, observations: Dict[str, Dict], deterministic: bool = False) -> Tuple[Dict, Dict, Dict]:
        """
        为所有智能体生成动作
        
        Args:
            observations: 所有智能体的观测
            deterministic: 是否使用确定性策略
        
        Returns:
            actions: 所有智能体的动作
            log_probs: 所有智能体的对数概率
            values: 所有智能体的价值
        """
        actions = {}
        log_probs = {}
        
        # 为每个智能体生成动作
        for agent_id, obs in observations.items():
            action, log_prob = self.actor.get_action(obs, deterministic)
            actions[agent_id] = action
            log_probs[agent_id] = log_prob
        
        # 计算价值
        values = self.critic.get_value(observations)
        
        return actions, log_probs, values
    
    def evaluate(self, observations: Dict[str, Dict]) -> Dict[str, np.ndarray]:
        """
        评估策略（确定性动作）
        
        Args:
            observations: 所有智能体的观测
        
        Returns:
            actions: 确定性动作
        """
        actions, _, _ = self.act(observations, deterministic=True)
        return actions
    
    def update(self, buffer) -> Dict[str, float]:
        """
        PPO更新
        
        Args:
            buffer: 经验缓冲区
        
        Returns:
            训练统计信息
        """
        # 计算GAE
        buffer.compute_returns_and_advantages(self.gamma, self.lamda)
        
        # 获取所有数据
        data = buffer.get_all_data()
        
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy = 0
        
        # PPO多轮更新
        for epoch in range(self.K_epochs):
            for batch in buffer.get_batches(self.config.mini_batch_size):
                # 重新计算动作概率和价值
                policy_loss, value_loss, entropy = self._compute_losses(batch)
                
                # 更新Actor
                self.optimizer_actor.zero_grad()
                actor_loss = policy_loss - self.entropy_coef * entropy
                actor_loss.backward()
                if self.use_grad_clip:
                    torch.nn.utils.clip_grad_norm_(self.actor.parameters(), self.max_grad_norm)
                self.optimizer_actor.step()
                
                # 更新Critic
                self.optimizer_critic.zero_grad()
                critic_loss = self.value_coef * value_loss
                critic_loss.backward()
                if self.use_grad_clip:
                    torch.nn.utils.clip_grad_norm_(self.critic.parameters(), self.max_grad_norm)
                self.optimizer_critic.step()
                
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy += entropy.item()
        
        # 返回统计信息
        n_updates = self.K_epochs * len(list(buffer.get_batches(self.config.mini_batch_size)))
        stats = {
            'policy_loss': total_policy_loss / n_updates,
            'value_loss': total_value_loss / n_updates,
            'entropy': total_entropy / n_updates,
        }
        
        return stats
    
    def _compute_losses(self, batch: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算策略损失、价值损失和熵
        
        Args:
            batch: 训练批次
        
        Returns:
            policy_loss, value_loss, entropy
        """
        # 提取批次数据
        obs = batch['observations']  # [batch_size, obs_dim]
        actions = batch['actions']   # [batch_size, seq_len]
        old_log_probs = batch['log_probs']  # [batch_size, seq_len]
        advantages = batch['advantages']     # [batch_size]
        returns = batch['returns']           # [batch_size]
        
        # 优势归一化
        if self.use_adv_norm:
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 从展平的观测重建序列输入
        batch_size = obs.shape[0]
        
        # 重建序列格式 (简化实现，假设obs按固定格式存储)
        # 实际应用中需要根据具体的观测结构调整
        task_queue = obs[:, :self.config.sequence_length * self.config.task_feature_dim].reshape(
            batch_size, self.config.sequence_length, self.config.task_feature_dim
        )
        
        # 创建虚拟掩码（简化处理）
        task_mask = torch.ones(batch_size, self.config.sequence_length)
        action_mask = torch.ones(batch_size, self.config.sequence_length, self.config.action_dim)
        
        # 计算当前策略的动作概率
        current_logits = self.actor(task_queue, task_mask, action_mask)
        current_dist = Categorical(logits=current_logits)
        current_log_probs = current_dist.log_prob(actions)
        
        # 计算重要性采样比率
        ratio = torch.exp(current_log_probs - old_log_probs)
        
        # PPO clipped loss
        surr1 = ratio * advantages.unsqueeze(-1)  # 扩展advantages维度
        surr2 = torch.clamp(ratio, 1 - self.epsilon, 1 + self.epsilon) * advantages.unsqueeze(-1)
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 计算当前价值
        # 简化：使用全零的全局状态
        global_state_dim = self.config.n_agents * 21
        dummy_global_state = torch.zeros(batch_size, global_state_dim)
        current_values = self.critic(dummy_global_state).squeeze(-1)
        
        # 价值损失
        value_loss = F.mse_loss(current_values, returns)
        
        # 熵奖励
        entropy = current_dist.entropy().mean()
        
        return policy_loss, value_loss, entropy
    
    def save(self, path: str):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'optimizer_actor_state_dict': self.optimizer_actor.state_dict(),
            'optimizer_critic_state_dict': self.optimizer_critic.state_dict(),
        }, path)
    
    def load(self, path: str):
        """加载模型"""
        checkpoint = torch.load(path)
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.optimizer_actor.load_state_dict(checkpoint['optimizer_actor_state_dict'])
        self.optimizer_critic.load_state_dict(checkpoint['optimizer_critic_state_dict'])


# ==================== 兼容接口 ====================

# 为了兼容参考代码的接口
PPO_discrete = TransformerPPO
Actor = TransformerActor
Critic = CentralizedCritic


if __name__ == "__main__":
    # 测试网络
    from src.agent.mappo_trans.config import get_default_config
    
    config = get_default_config()
    
    # 测试Actor
    actor = TransformerActor(config)
    
    # 创建虚拟输入
    batch_size = 2
    seq_len = 20
    task_dim = 8
    action_dim = 16
    
    task_queue = torch.randn(batch_size, seq_len, task_dim)
    task_mask = torch.randint(0, 2, (batch_size, seq_len)).float()
    action_mask = torch.randint(0, 2, (batch_size, seq_len, action_dim)).float()
    
    # 前向传播
    logits = actor(task_queue, task_mask, action_mask)
    print(f"Actor output shape: {logits.shape}")
    
    # 测试Critic
    critic = CentralizedCritic(config)
    global_state = torch.randn(batch_size, config.n_agents * 21)
    values = critic(global_state)
    print(f"Critic output shape: {values.shape}")
    
    # 测试完整MAPPO
    mappo = TransformerPPO(config)
    print("MAPPO initialized successfully")
    
    print("Transformer PPO test completed!")