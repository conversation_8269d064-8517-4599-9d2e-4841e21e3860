"""
Configuration parameters for MAPPO on SPACE3 environment
配置参数 - 严格遵循CLAUDE.md编程规范，从真实config.yaml读取
"""

import os
import yaml
from dataclasses import dataclass
from typing import Optional
from pathlib import Path

# Use absolute path resolution
project_root = Path(__file__).parent.parent.parent.parent.absolute()


class MAPPOConfig:
    """MAPPO训练配置参数 - 从真实配置文件读取，不使用硬编码默认值"""
    
    def __init__(self, 
                 physics_config_path: str = None,
                 env_config_path: str = None):
        """
        从真实配置文件初始化，遵循CLAUDE.md编程规范
        
        Args:
            physics_config_path: 物理层配置文件路径
            env_config_path: 环境配置文件路径
        """
        # 使用真实配置文件路径，不用硬编码
        if physics_config_path is None:
            physics_config_path = os.path.join(project_root, "src/env/physics_layer/config.yaml")
        if env_config_path is None:
            env_config_path = os.path.join(project_root, "src/env/space_env/env_config.yaml")
        
        # 加载真实配置文件
        self._load_configs(physics_config_path, env_config_path)
        
        # 基于真实配置计算衍生参数
        self._compute_derived_params()
    
    def _load_configs(self, physics_config_path: str, env_config_path: str):
        """加载配置文件，包含结构化异常处理"""
        try:
            # 加载物理层配置
            with open(physics_config_path, 'r', encoding='utf-8') as f:
                self.physics_config = yaml.safe_load(f)
                
        except FileNotFoundError:
            raise FileNotFoundError(f"物理配置文件未找到: {physics_config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"物理配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载物理配置失败: {e}")
        
        try:
            # 加载环境配置
            with open(env_config_path, 'r', encoding='utf-8') as f:
                self.env_config = yaml.safe_load(f)
                
        except FileNotFoundError:
            raise FileNotFoundError(f"环境配置文件未找到: {env_config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"环境配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载环境配置失败: {e}")
    
    def _compute_derived_params(self):
        """基于真实配置计算参数，严格按照CLAUDE.md规范"""
        
        # ==================== 环境参数（从真实配置读取）====================
        self.env_name = "SPACE3"
        self.algo_name = "MAPPO_Transformer"
        self.device = "cpu"
        self.seed = 42
        
        # SPACE3环境特定参数（从physics_config读取真实值）
        system_config = self.physics_config['system']
        self.n_agents = system_config['num_leo_satellites']  # 真实值：72
        self.max_timeslots = system_config['total_timeslots']  # 真实值：2000，不是100！
        self.timeslot_duration = system_config['timeslot_duration_s']  # 真实值：3秒，不是5秒！
        
        # 环境配置（从env_config读取）
        self.max_queue_size = self.env_config['max_queue_size']  # 20
        self.task_feature_dim = self.env_config['task_feature_dim']  # 8
        self.state_feature_dim = self.env_config['state_feature_dim']  # 12
        self.neighbor_feature_dim = self.env_config['neighbor_feature_dim']  # 5
        self.max_visible_neighbors = self.env_config['max_visible_neighbors']  # 10
        self.num_cloud_targets = self.env_config['num_cloud_targets']  # 5
        
        # ==================== 观测和动作空间 ====================
        self.action_dim = 16  # 每个任务的可选动作数
        self.sequence_length = self.max_queue_size  # 动作序列长度
        
        # ==================== 网络架构参数 ====================
        self.hidden_dim = 128
        self.embed_dim = 128
        self.n_heads = 4
        self.n_layers = 1
        self.dropout = 0.1
        
        # ==================== PPO算法参数 ====================
        self.lr_a = 3e-4
        self.lr_c = 3e-4
        self.gamma = 0.99
        self.lamda = 0.95
        self.epsilon = 0.2
        self.K_epochs = 4
        self.entropy_coef = 0.01
        self.value_coef = 0.5
        self.max_grad_norm = 0.5
        
        # ==================== 训练配置 ====================
        self.batch_size = 256
        self.rollout_length = 128
        self.buffer_size = 2048
        self.mini_batch_size = 64
        
        # 基于真实时隙数调整训练步数
        self.total_timesteps = min(10000, self.max_timeslots * 5)  # 适应真实时隙数
        self.max_train_steps = self.total_timesteps
        
        # ==================== 评估和保存 ====================
        self.eval_freq = 5
        self.save_freq = 10
        self.log_freq = 1
        
        # 路径配置
        self.model_dir = os.path.join(project_root, "src/agent/mappo_trans/models")
        self.log_dir = os.path.join(project_root, "src/agent/mappo_trans/logs")
        
        # ==================== 优化技巧开关 ====================
        self.use_orthogonal_init = False
        self.use_tanh = False
        self.set_adam_eps = True
        self.use_grad_clip = True
        self.use_lr_decay = False
        self.use_adv_norm = True
        self.use_state_norm = False
        self.use_reward_norm = False
        
        # ==================== MAPPO特定参数 ====================
        self.centralized_critic = True
        self.shared_policy = True
        
        # ==================== 调试和日志 ====================
        self.debug = False
        self.verbose = True
        self.render = False
        
        # 确保目录存在
        os.makedirs(self.model_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 计算衍生参数
        self.n_iterations = self.total_timesteps // self.rollout_length
        self.obs_dim = self._calculate_obs_dim()
        self.state_dim = self.obs_dim  # 与参考代码兼容
    
        
    def _calculate_obs_dim(self) -> int:
        """计算展平后的观测维度 - 修复版本"""
        # 基于环境手册的观测结构计算，与utils.py中的extract_features_from_obs保持一致
        own_state_dim = self.state_feature_dim  # 12
        task_queue_dim = self.max_queue_size * self.task_feature_dim  # 20 * 8 = 160
        task_mask_dim = self.max_queue_size  # 20
        action_mask_dim = self.max_queue_size * self.action_dim  # 20 * 16 = 320
        neighbor_states_dim = self.max_visible_neighbors * self.neighbor_feature_dim  # 10 * 5 = 50
        comm_quality_dim = self.max_visible_neighbors  # 10
        time_info_dim = 3  # 3
        
        total_dim = (own_state_dim + task_queue_dim + task_mask_dim + 
                    action_mask_dim + neighbor_states_dim + comm_quality_dim + time_info_dim)
        
        # 验证计算结果
        expected_total = 575  # 12 + 160 + 20 + 320 + 50 + 10 + 3
        assert total_dim == expected_total, f"观测维度计算错误: {total_dim} != {expected_total}"
        
        if self.verbose:
            print(f"MAPPO Config - obs_dim validation: {total_dim}")
        
        return total_dim
    
    def get_model_path(self, filename: str) -> str:
        """获取模型保存路径"""
        return os.path.join(self.model_dir, filename)
    
    def get_log_path(self, filename: str) -> str:
        """获取日志保存路径"""
        return os.path.join(self.log_dir, filename)


def get_default_config() -> MAPPOConfig:
    """获取默认配置"""
    return MAPPOConfig()


def parse_args() -> MAPPOConfig:
    """解析命令行参数（简化版）"""
    # 简化版本直接返回默认配置
    # 在更完整的版本中可以添加argparse
    return get_default_config()


if __name__ == "__main__":
    # 测试配置
    config = get_default_config()
    print(f"Algorithm: {config.algo_name}")
    print(f"Environment: {config.env_name}")
    print(f"Agents: {config.n_agents}")
    print(f"Observation dimension: {config.obs_dim}")
    print(f"Action dimension: {config.action_dim}")
    print(f"Sequence length: {config.sequence_length}")
    print(f"Total iterations: {config.n_iterations}")
    print(f"Model directory: {config.model_dir}")