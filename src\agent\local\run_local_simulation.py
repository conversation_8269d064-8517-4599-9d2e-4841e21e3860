"""
Run local computation simulation for SPACE3
This script runs the baseline local-only processing simulation
"""

import sys
import os
from pathlib import Path
import argparse
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Absolute imports
from src.agent.local.local_compute_simulator import LocalComputeSimulator


def export_results_to_csv(result, simulator, output_dir: str):
    """
    Export simulation results to CSV files
    
    Args:
        result: SimulationResult object
        simulator: LocalComputeSimulator object
        output_dir: Output directory for CSV files
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 1. Export timeslot results
    timeslot_data = []
    for ts_result in result.timeslot_results:
        timeslot_data.append({
            'timeslot': ts_result.timeslot,
            'simulation_time_s': ts_result.timeslot * 3,  # 3 seconds per timeslot
            'tasks_generated': ts_result.tasks_generated,
            'tasks_assigned': ts_result.tasks_assigned,
            'tasks_completed': len(ts_result.tasks_completed),
            'tasks_dropped': len(ts_result.tasks_dropped),
            'tasks_in_progress': ts_result.tasks_in_progress,
            'avg_battery_level': np.mean(list(ts_result.satellite_energy_states.values())) if ts_result.satellite_energy_states else 0
        })
    
    df_timeslots = pd.DataFrame(timeslot_data)
    df_timeslots.to_csv(output_path / 'timeslot_results.csv', index=False)
    print(f"Exported timeslot results to {output_path / 'timeslot_results.csv'}")
    
    # 2. Export completed tasks details
    completed_tasks_data = []
    for task in simulator.all_completed_tasks:
        completed_tasks_data.append({
            'task_id': task.task_id,
            'task_type': task.task_type.value if hasattr(task, 'task_type') else 'unknown',
            'data_size_mb': task.data_size if hasattr(task, 'data_size') else 0,
            'complexity': task.complexity if hasattr(task, 'complexity') else 0,
            'priority': task.priority if hasattr(task, 'priority') else 0,
            'arrival_time': task.arrival_time if hasattr(task, 'arrival_time') else 0,
            'completion_time': task.completion_time if hasattr(task, 'completion_time') else 0,
            'processing_delay': task.actual_total_delay if hasattr(task, 'actual_total_delay') else 0,
            'energy_consumed': task.energy_consumed if hasattr(task, 'energy_consumed') else 0,
            'assigned_satellite': task.assigned_satellite_id if hasattr(task, 'assigned_satellite_id') else 'unknown'
        })
    
    if completed_tasks_data:
        df_completed = pd.DataFrame(completed_tasks_data)
        df_completed.to_csv(output_path / 'completed_tasks.csv', index=False)
        print(f"Exported completed tasks to {output_path / 'completed_tasks.csv'}")
    
    # 3. Export satellite statistics
    satellite_stats_data = []
    for sat_id, stats in result.satellite_statistics.items():
        satellite_stats_data.append({
            'satellite_id': sat_id,
            'tasks_processed': stats['tasks_processed'],
            'tasks_dropped': stats['tasks_dropped'],
            'energy_consumed': stats['energy_consumed'],
            'final_battery': stats['final_battery'],
            'utilization': stats['utilization']
        })
    
    if satellite_stats_data:
        df_satellites = pd.DataFrame(satellite_stats_data)
        df_satellites.to_csv(output_path / 'satellite_statistics.csv', index=False)
        print(f"Exported satellite statistics to {output_path / 'satellite_statistics.csv'}")
    
    # 4. Export performance metrics summary
    summary_data = [{
        'metric': 'total_tasks_generated',
        'value': result.total_tasks_generated
    }, {
        'metric': 'total_tasks_completed',
        'value': result.total_tasks_completed
    }, {
        'metric': 'total_tasks_dropped',
        'value': result.total_tasks_dropped
    }, {
        'metric': 'overall_completion_rate',
        'value': result.overall_completion_rate
    }, {
        'metric': 'avg_delay_per_task_s',
        'value': result.avg_delay_per_task
    }, {
        'metric': 'avg_energy_per_task_j',
        'value': result.avg_energy_per_task
    }]
    
    if result.performance_metrics:
        summary_data.extend([
            {'metric': 'mean_delay_s', 'value': result.performance_metrics.mean_delay},
            {'metric': 'median_delay_s', 'value': result.performance_metrics.median_delay},
            {'metric': 'p90_delay_s', 'value': result.performance_metrics.p90_delay},
            {'metric': 'p99_delay_s', 'value': result.performance_metrics.p99_delay},
            {'metric': 'total_energy_consumed_j', 'value': result.performance_metrics.total_energy_consumed},
            {'metric': 'realtime_completion_rate', 'value': result.performance_metrics.type1_completion_rate},
            {'metric': 'normal_completion_rate', 'value': result.performance_metrics.type2_completion_rate},
            {'metric': 'compute_intensive_completion_rate', 'value': result.performance_metrics.type3_completion_rate}
        ])
    
    df_summary = pd.DataFrame(summary_data)
    df_summary.to_csv(output_path / 'performance_summary.csv', index=False)
    print(f"Exported performance summary to {output_path / 'performance_summary.csv'}")
    
    # 5. Export completion rates by priority
    priority_data = []
    for priority, rate in result.completion_rate_by_priority.items():
        priority_data.append({
            'priority': priority,
            'completion_rate': rate
        })
    
    if priority_data:
        df_priority = pd.DataFrame(priority_data)
        df_priority.to_csv(output_path / 'completion_by_priority.csv', index=False)
        print(f"Exported completion by priority to {output_path / 'completion_by_priority.csv'}")


def main():
    """Main function to run local computation simulation"""
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run SPACE3 local computation simulation')
    parser.add_argument('--timeslots', type=int, default=100,
                       help='Number of timeslots to simulate (default: 100)')
    parser.add_argument('--config', type=str, default=None,
                       help='Path to configuration file')
    parser.add_argument('--output', type=str, default='local_simulation_results',
                       help='Output file prefix for results (default: local_simulation_results)')
    parser.add_argument('--csv-output', type=str, default='simulation_output',
                       help='CSV output directory (default: simulation_output)')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Initialize logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("="*60)
    print(" SPACE3 Local Computation Simulation")
    print("="*60)
    print(f"Timeslots: {args.timeslots}")
    print(f"JSON Output: {args.output}.json")
    print(f"CSV Output: {args.csv_output}/")
    print("="*60)
    
    try:
        # Create simulator
        print("\n[1/3] Initializing simulator...")
        start_time = time.time()
        simulator = LocalComputeSimulator(config_path=args.config)
        
        # Run simulation with timeslot display
        print(f"\n[2/3] Running simulation for {args.timeslots} timeslots...")
        print("This may take a few minutes...")
        print("Progress: (Every 10 timeslots will be displayed)")
        
        # Initialize components manually to track progress
        simulator._initialize_components()
        simulator.timeslot_results = []
        simulator.all_completed_tasks = []
        simulator.all_dropped_tasks = []
        simulator.all_tasks_map = {}
        
        # Run simulation with progress display
        for timeslot in range(args.timeslots):
            # Display progress every 10 timeslots or at key intervals
            if timeslot % 10 == 0 or timeslot == args.timeslots - 1:
                progress = (timeslot + 1) / args.timeslots * 100
                print(f"  Timeslot {timeslot + 1}/{args.timeslots} ({progress:.1f}%)")
            
            # Process single timeslot
            ts_result = simulator._process_single_timeslot(timeslot)
            simulator.timeslot_results.append(ts_result)
        
        # Calculate final results
        total_generated = sum(r.tasks_generated for r in simulator.timeslot_results)
        total_completed = len(simulator.all_completed_tasks)
        total_dropped = len(simulator.all_dropped_tasks)
        
        # Calculate metrics
        avg_delay = 0.0
        avg_energy = 0.0
        
        if simulator.all_completed_tasks:
            delays = []
            energies = []
            for task in simulator.all_completed_tasks:
                if hasattr(task, 'actual_total_delay') and task.actual_total_delay > 0:
                    delays.append(task.actual_total_delay)
                elif task.completion_time and task.arrival_time:
                    delays.append(task.completion_time - task.arrival_time)
                
                if task.energy_consumed > 0:
                    energies.append(task.energy_consumed)
            
            if delays:
                avg_delay = np.mean(delays)
            if energies:
                avg_energy = np.mean(energies)
        
        # Calculate completion rates by priority
        completion_by_priority = {}
        for priority in range(1, 11):
            gen = simulator.task_stats_by_priority[priority]['generated']
            comp = simulator.task_stats_by_priority[priority]['completed']
            completion_by_priority[priority] = comp / gen if gen > 0 else 0
        
        # Calculate satellite statistics
        satellite_stats = {}
        for sat_id, satellite in simulator.satellites.items():
            satellite_stats[sat_id] = {
                'tasks_processed': satellite.total_tasks_processed,
                'tasks_dropped': satellite.total_tasks_dropped,
                'energy_consumed': satellite.total_energy_consumed,
                'final_battery': satellite.battery_energy,
                'utilization': satellite.total_tasks_processed / max(1, args.timeslots)
            }
        
        # Create simulation result
        from src.agent.local.simulation_result import SimulationResult
        result = SimulationResult(
            total_tasks_generated=total_generated,
            total_tasks_completed=total_completed,
            total_tasks_dropped=total_dropped,
            overall_completion_rate=total_completed / max(1, total_generated),
            avg_delay_per_task=avg_delay,
            avg_energy_per_task=avg_energy,
            completion_rate_by_priority=completion_by_priority,
            performance_metrics=simulator._calculate_performance_metrics(),
            timeslot_results=simulator.timeslot_results,
            satellite_statistics=satellite_stats
        )
        
        elapsed_time = time.time() - start_time
        print(f"\nSimulation completed in {elapsed_time:.2f} seconds")
        
        # Print summary
        result.print_summary()
        
        # Export results
        print(f"\n[3/4] Exporting JSON results to {args.output}.json...")
        simulator.export_results(result, f"{args.output}.json")
        
        # Export CSV results
        print(f"\n[4/4] Exporting CSV results to {args.csv_output}/...")
        export_results_to_csv(result, simulator, args.csv_output)
        
        print("\n" + "="*60)
        print(" Simulation Complete!")
        print("="*60)
        
        # Print key metrics
        print("\nKey Performance Indicators:")
        print(f"  Total Timeslots Processed: {args.timeslots}")
        print(f"  Total Simulation Time: {args.timeslots * 3} seconds")
        print(f"  Overall Completion Rate: {result.overall_completion_rate:.1%}")
        print(f"  Average Delay: {result.avg_delay_per_task:.2f} seconds")
        print(f"  Average Energy per Task: {result.avg_energy_per_task:.2f} Joules")
        
        if result.performance_metrics:
            print(f"\nTask Type Completion Rates:")
            print(f"  REALTIME: {result.performance_metrics.type1_completion_rate:.1%}")
            print(f"  NORMAL: {result.performance_metrics.type2_completion_rate:.1%}")
            print(f"  COMPUTE_INTENSIVE: {result.performance_metrics.type3_completion_rate:.1%}")
        
        print(f"\nOutput Files Generated:")
        print(f"  JSON Results: {args.output}.json")
        print(f"  CSV Directory: {args.csv_output}/")
        print(f"    - timeslot_results.csv (per-timeslot metrics)")
        print(f"    - completed_tasks.csv (individual task details)")
        print(f"    - satellite_statistics.csv (satellite performance)")
        print(f"    - performance_summary.csv (overall metrics)")
        print(f"    - completion_by_priority.csv (priority analysis)")
        
        return 0
        
    except Exception as e:
        print(f"\nError: {e}")
        logging.exception("Simulation failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())