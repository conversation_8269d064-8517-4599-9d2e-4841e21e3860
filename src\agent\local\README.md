# SPACE3 本地运算仿真器 (Local Computation Simulator)

## 概述

本模块实现了SPACE3卫星边缘计算环境的**本地运算基线算法**。所有任务仅在接收卫星本地处理，不进行任务卸载或协同调度。该实现作为性能对比的基准线。

## 特性

- ✅ **完全本地处理**：任务仅在接收卫星处理，无任务卸载
- ✅ **DPSQ调度算法**：使用动态优先级评分队列算法进行本地调度
- ✅ **能量约束管理**：考虑卫星电池容量和太阳能充电
- ✅ **性能指标分析**：计算延迟、能耗、完成率等关键指标
- ✅ **真实数据驱动**：使用真实卫星轨道数据和地面站位置

## 文件结构

```
src/agent/local/
├── __init__.py                    # 模块初始化
├── local_compute_simulator.py     # 主仿真器类
├── simulation_result.py           # 结果数据模型
├── run_local_simulation.py        # 运行脚本
└── README.md                      # 本文档

test/agent/local/
└── test_local_compute.py         # 测试套件
```

## 快速开始

### 1. 运行仿真

```bash
# 从项目根目录运行
cd D:\paper\space\SPACE3

# 运行100个时隙的仿真
python src/agent/local/run_local_simulation.py --timeslots 100

# 运行完整仿真（2000时隙）
python src/agent/local/run_local_simulation.py --timeslots 2000

# 启用详细日志
python src/agent/local/run_local_simulation.py --timeslots 100 --verbose
```

### 2. 运行测试

```bash
# 运行单元测试
python test/agent/local/test_local_compute.py
```

### 3. Python API使用

```python
from src.agent.local.local_compute_simulator import LocalComputeSimulator

# 创建仿真器
simulator = LocalComputeSimulator()

# 运行仿真
result = simulator.run_simulation(total_timeslots=100)

# 打印结果摘要
result.print_summary()

# 导出结果
simulator.export_results(result, "results.json")
```

## 性能指标

仿真器计算以下关键性能指标：

### 延迟指标
- **平均延迟**：所有完成任务的平均端到端延迟
- **延迟分布**：P50、P90、P99延迟百分位数
- **任务类型延迟**：REALTIME、NORMAL、COMPUTE_INTENSIVE各类任务延迟

### 能耗指标
- **总能耗**：所有卫星的总能量消耗
- **单任务能耗**：平均每个任务的能量消耗
- **能效**：每千焦完成的任务数

### 完成率指标
- **总体完成率**：完成任务数/生成任务数
- **按类型完成率**：三种任务类型的各自完成率
- **按优先级完成率**：高(8-10)、中(4-7)、低(1-3)优先级任务完成率

## 配置参数

仿真使用 `src/env/physics_layer/config.yaml` 中的参数：

```yaml
system:
  num_leo_satellites: 72      # LEO卫星数量
  num_users: 420              # 地面用户数量
  timeslot_duration_s: 3      # 时隙持续时间（秒）
  total_timeslots: 2000       # 总时隙数

computation:
  f_leo_hz: 500000000000      # 卫星CPU频率 (500GHz)
  leo_battery_capacity_j: 3600000  # 电池容量 (3.6MJ)
  leo_solar_power_w: 5000     # 太阳能板功率 (5kW)

queuing:
  max_queue_size: 100         # 最大队列长度
  w_priority: 1.0             # 优先级权重
  w_urgency: 2.0              # 紧迫性权重
  w_cost: 0.5                 # 成本权重
```

## 输出格式

仿真结果以JSON格式导出，包含：

```json
{
  "summary": {
    "total_generated": 84000,
    "total_completed": 50400,
    "total_dropped": 33600,
    "overall_completion_rate": 0.6,
    "avg_delay": 45.3,
    "avg_energy": 2850.0
  },
  "completion_by_priority": {
    "1": 0.43, "2": 0.45, ... "10": 0.88
  },
  "performance_metrics": {
    "delay_metrics": {...},
    "energy_metrics": {...},
    "completion_by_type": {...},
    "completion_by_priority": {...}
  },
  "satellite_stats": {
    "0": {"tasks_processed": 698, ...},
    ...
  }
}
```

## 性能基准

在标准配置下的典型性能：

| 指标 | 值 |
|------|-----|
| 总体完成率 | 55-65% |
| 平均延迟 | 40-50秒 |
| REALTIME完成率 | 75-85% |
| NORMAL完成率 | 60-70% |
| COMPUTE_INTENSIVE完成率 | 35-45% |
| 每任务能耗 | 2500-3500 J |

## 注意事项

1. **内存使用**：2000时隙仿真约需2-3GB内存
2. **运行时间**：2000时隙仿真约需5-10分钟
3. **确定性**：使用固定随机种子确保结果可重现
4. **原子任务**：任务不可分割，必须在单个卫星完整处理

## 后续扩展

本基线实现为后续算法提供对比基准：

- **PSO优化**：全局优化的任务分配
- **MAPPO强化学习**：多智能体协同调度
- **任务卸载**：卫星间协作处理

## 联系方式

如有问题，请查看项目文档或提交Issue。