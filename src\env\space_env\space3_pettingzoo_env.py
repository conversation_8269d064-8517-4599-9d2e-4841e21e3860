"""
SPACE3 PettingZoo Environment for Transformer-MAPPO
专门为序列动作强化学习设计的卫星任务调度环境
"""

import numpy as np
import os
from pettingzoo import ParallelEnv
from gymnasium import spaces
from typing import Dict, List, Optional, Tuple, Any
import yaml
from datetime import datetime
import logging

# 导入现有SPACE3模块
from src.env.Foundation_Layer.time_manager import create_time_manager_from_config
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_distributor import TaskDistributor  # 添加TaskDistributor导入
from src.env.physics_layer.task_models import Task, AssignmentStatus, TaskType, TaskAssignment
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.satellite_cloud.cloud_compute import CloudCompute
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus as ComputeTaskStatus
# from src.env.metrics_model.algorithm_metrics import AlgorithmMetrics  # 暂时注释，避免导入错误

# 导入环境组件
from .observation_builder import ObservationBuilder
from .reward_calculator import RewardCalculator
from .sequence_action_handler import SequenceActionHandler

# 设置日志
logger = logging.getLogger(__name__)


class Space3PettingZooEnv(ParallelEnv):
    """
    SPACE3 并行多智能体环境
    支持Transformer-MAPPO的序列动作空间
    """
    
    metadata = {
        "render_modes": ["human", "rgb_array"],
        "name": "space3_transformer_v0",
    }
    
    def __init__(self, config_path: str = None):
        """
        初始化环境
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        super().__init__()
        
        # 加载配置
        import os
        self.base_dir = self._get_project_root()
        
        if config_path is None:
            config_path = os.path.join(self.base_dir, "src", "env", "space_env", "env_config.yaml")
        
        # 尝试加载环境配置，如果不存在则抛出错误
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.env_config = yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Environment config not found at {config_path}")
            raise FileNotFoundError(f"Required environment config file not found: {config_path}. "
                                   "Please ensure the config file exists or provide a valid config_path.")
        
        # 验证配置完整性
        self._validate_env_config(self.env_config)
        
        # 加载系统配置
        system_config_path = self._get_config_path('base_config')
        with open(system_config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 合并配置
        self.config['rl_env'] = self.env_config
        
        # 初始化基础模块
        self._initialize_modules()
        
        # 初始化环境参数
        self._setup_environment_params()
        
        # 定义动作和观测空间
        self._setup_spaces()
        
        # 初始化环境组件
        self._initialize_components()
        
        # 初始化状态变量
        self._reset_state_variables()
        
        logger.info("Space3PettingZooEnv initialized successfully")
    
    def _get_project_root(self) -> str:
        """
        获取项目根目录
        
        Returns:
            项目根目录路径
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 从当前目录向上查找项目根目录（包含src目录的父目录）
        while current_dir != os.path.dirname(current_dir):
            if os.path.exists(os.path.join(current_dir, 'src')):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        
        # 如果找不到，使用相对路径计算
        return os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    def _get_config_path(self, path_key: str) -> str:
        """
        从配置中获取数据路径
        
        Args:
            path_key: 路径配置键名
            
        Returns:
            完整的文件路径
            
        Raises:
            KeyError: 当路径配置不存在时
        """
        data_paths = self.env_config.get('data_paths', {})
        if path_key not in data_paths:
            raise KeyError(f"Path configuration '{path_key}' not found in env_config.data_paths")
        
        relative_path = data_paths[path_key]
        return os.path.join(self.base_dir, relative_path)
    
    def _validate_env_config(self, config: Dict):
        """
        验证环境配置完整性
        
        Args:
            config: 环境配置字典
            
        Raises:
            ValueError: 当配置缺少必需字段或值无效时
        """
        required_keys = [
            'max_queue_size', 'task_feature_dim', 'state_feature_dim',
            'neighbor_feature_dim', 'max_visible_neighbors', 'num_cloud_targets',
            'reward_weights', 'data_paths'
        ]
        
        # 检查必需字段
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            raise ValueError(f"Missing required config keys: {missing_keys}")
        
        # 检查奖励权重配置
        required_reward_keys = ['completion', 'drop', 'delay', 'energy', 'invalid_action']
        reward_weights = config.get('reward_weights', {})
        missing_reward_keys = [key for key in required_reward_keys if key not in reward_weights]
        if missing_reward_keys:
            raise ValueError(f"Missing required reward weight keys: {missing_reward_keys}")
        
        # 检查数值范围
        if config['max_queue_size'] <= 0:
            raise ValueError("max_queue_size must be positive")
        if config['max_visible_neighbors'] <= 0:
            raise ValueError("max_visible_neighbors must be positive")
        if config['num_cloud_targets'] <= 0:
            raise ValueError("num_cloud_targets must be positive")
        
        # 检查路径配置
        required_paths = ['ground_stations', 'base_config']
        data_paths = config.get('data_paths', {})
        missing_paths = [path for path in required_paths if path not in data_paths]
        if missing_paths:
            raise ValueError(f"Missing required data path keys: {missing_paths}")
        
        logger.info("Environment configuration validation passed")
    
    def _initialize_modules(self):
        """初始化所有SPACE3模块"""
        # 时间管理器
        self.time_manager = create_time_manager_from_config(self.config)
        
        # 轨道动力学
        self.orbital_updater = OrbitalUpdater()
        
        # 通信管理器
        self.communication_manager = CommunicationManager(
            orbital_updater=self.orbital_updater,
            config_file=self._get_config_path('base_config'),
            time_manager=self.time_manager
        )
        
        # 任务生成器（传入None让它使用自己的配置加载）
        self.task_generator = TaskGenerator(None)
        
        # 任务分配器 - 使用最近距离原则
        self.task_distributor = TaskDistributor(
            orbital_updater=self.orbital_updater,
            config_file=self._get_config_path('base_config'),
            time_manager=self.time_manager
        )
        
        # 加载地面站位置数据到任务生成器
        ground_stations_path = self._get_config_path('ground_stations')
        self.task_generator.load_locations_from_csv(ground_stations_path)
        
        # 性能分析器
        # self.metrics_analyzer = AlgorithmMetrics()  # 暂时禁用，避免导入错误
        
        # 初始化卫星计算节点
        self.num_satellites = self.config['system']['num_leo_satellites']
        self.satellites = {}
        for i in range(self.num_satellites):
            # 使用实际的卫星ID (从111开始，匹配数据文件)
            sat_id = f"sat_{111 + i}"
            self.satellites[sat_id] = SatelliteCompute(
                satellite_id=sat_id,
                config=self.config
            )
        
        # 初始化云计算节点
        self.num_clouds = self.config['system']['num_cloud_centers']
        self.cloud_nodes = {}
        for i in range(self.num_clouds):
            cloud_id = f"cloud_{i}"
            self.cloud_nodes[cloud_id] = CloudCompute(
                cloud_id=cloud_id,
                config=self.config
            )
    
    def _setup_environment_params(self):
        """设置环境参数"""
        # RL环境参数
        self.max_queue_size = self.env_config.get('max_queue_size', 20)
        self.task_feature_dim = self.env_config.get('task_feature_dim', 8)
        self.state_feature_dim = self.env_config.get('state_feature_dim', 12)
        self.neighbor_feature_dim = self.env_config.get('neighbor_feature_dim', 5)
        self.max_visible_neighbors = self.env_config.get('max_visible_neighbors', 10)
        self.num_cloud_targets = self.env_config.get('num_cloud_targets', 5)
        
        # 奖励权重
        self.reward_weights = self.env_config.get('reward_weights', {
            'completion': 10.0,
            'delay': -0.01,
            'energy': -0.001,
            'drop': -5.0,
            'invalid_action': -1.0
        })
        
        # 动作空间大小
        self.num_action_targets = 1 + self.max_visible_neighbors + self.num_cloud_targets
        
        # PettingZoo智能体
        self.possible_agents = list(self.satellites.keys())
        self.agents = self.possible_agents[:]
        
        # 仿真参数
        self.max_timeslots = self.config['system'].get('num_timeslots', 
                                                       self.config['system'].get('total_timeslots', 2000))
        self.timeslot_duration = self.config['system']['timeslot_duration_s']
    
    def _setup_spaces(self):
        """定义观测和动作空间"""
        # 观测空间（每个智能体相同）
        self.observation_spaces = {}
        for agent in self.possible_agents:
            self.observation_spaces[agent] = spaces.Dict({
                # 卫星自身状态
                'own_state': spaces.Box(
                    low=0, high=1,
                    shape=(self.state_feature_dim,),
                    dtype=np.float32
                ),
                
                # 任务队列（填充到固定长度）
                'task_queue': spaces.Box(
                    low=0, high=1,
                    shape=(self.max_queue_size, self.task_feature_dim),
                    dtype=np.float32
                ),
                
                # 任务掩码（标识真实任务）
                'task_mask': spaces.Box(
                    low=0, high=1,
                    shape=(self.max_queue_size,),
                    dtype=np.int8
                ),
                
                # 动作掩码（每个任务的可行动作）
                'action_mask': spaces.Box(
                    low=0, high=1,
                    shape=(self.max_queue_size, self.num_action_targets),
                    dtype=np.int8
                ),
                
                # 邻居状态
                'neighbor_states': spaces.Box(
                    low=0, high=1,
                    shape=(self.max_visible_neighbors, self.neighbor_feature_dim),
                    dtype=np.float32
                ),
                
                # 通信质量矩阵（到邻居的链路质量）
                'comm_quality': spaces.Box(
                    low=0, high=1,
                    shape=(self.max_visible_neighbors,),
                    dtype=np.float32
                ),
                
                # 时间信息
                'time_info': spaces.Box(
                    low=0, high=1,
                    shape=(3,),  # [当前进度, 剩余时隙比例, 一天中的时间]
                    dtype=np.float32
                )
            })
        
        # 动作空间（序列动作）
        self.action_spaces = {}
        for agent in self.possible_agents:
            # 每个位置可以选择一个目标（0:本地, 1-N:邻居, N+1-M:云）
            self.action_spaces[agent] = spaces.MultiDiscrete(
                [self.num_action_targets] * self.max_queue_size
            )
    
    def _initialize_components(self):
        """初始化环境组件（观测构建器、动作处理器、奖励计算器）"""
        # 创建观测构建器
        self.observation_builder = ObservationBuilder(
            max_queue_size=self.max_queue_size,
            task_feature_dim=self.task_feature_dim,
            state_feature_dim=self.state_feature_dim,
            neighbor_feature_dim=self.neighbor_feature_dim,
            num_action_targets=self.num_action_targets,
            max_visible_neighbors=self.max_visible_neighbors
        )
        
        # 创建序列动作处理器
        self.action_handler = SequenceActionHandler(self.env_config)
        
        # 创建奖励计算器
        self.reward_calculator = RewardCalculator(self.env_config)
        
        logger.info("环境组件初始化完成")
    
    def _reset_state_variables(self):
        """重置状态变量"""
        self.current_timeslot = 0
        self.task_queues = {agent: [] for agent in self.agents}
        
        # 重置TaskDistributor状态
        if hasattr(self, 'task_distributor'):
            self.task_distributor.reset()
        self.episode_metrics = []
        self.cumulative_rewards = {agent: 0.0 for agent in self.agents}
        
        # 跟踪变量
        self.total_tasks_generated = 0
        self.total_tasks_completed = 0
        self.total_tasks_dropped = 0
        
        # 可见性和通信缓存
        self.current_visibility_matrices = None
        self.current_comm_matrix = None
        self.satellite_positions = None
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """
        重置环境到初始状态
        
        Returns:
            observations: 所有智能体的初始观测
            infos: 额外信息
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 重置状态变量
        self._reset_state_variables()
        
        # 重置所有卫星
        for satellite in self.satellites.values():
            satellite.reset()
        
        # 重置云节点
        for cloud in self.cloud_nodes.values():
            cloud.reset()
        
        # 获取初始时间上下文
        time_context = self.time_manager.get_time_context(0)
        
        # 更新初始物理状态
        # 使用真实的轨道数据获取可见性矩阵
        self.current_visibility_matrices = self._get_real_visibility_matrices(0)
        # 获取通信矩阵
        self.current_comm_matrix = {
            'isl': self.communication_manager.get_isl_communication_matrix(0),
            'satellite_ground': self.communication_manager.get_satellite_ground_communication_matrix(0),
            'satellite_cloud': self.communication_manager.get_satellite_cloud_communication_matrix(0)
        }
        
        # 生成初始任务
        initial_tasks_by_location = self.task_generator.generate_tasks_for_timeslot(0)
        # 展平任务字典为任务列表
        all_initial_tasks = []
        for location_id, tasks in initial_tasks_by_location.items():
            all_initial_tasks.extend(tasks)
        # 使用最近距离原则分配初始任务
        self._distribute_initial_tasks(all_initial_tasks)
        logger.info(f"Distributed {len(all_initial_tasks)} initial tasks using nearest distance principle")
        
        # 构建初始观测
        observations = self._build_all_observations()
        
        # 初始化信息
        infos = {agent: {'reset': True} for agent in self.agents}
        
        logger.info(f"Environment reset with {len(all_initial_tasks)} initial tasks")
        
        return observations, infos
    
    def step(self, actions: Dict[str, np.ndarray]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
        """
        执行一步环境更新
        
        Args:
            actions: 每个智能体的动作序列 {agent_id: [action_0, ..., action_19]}
        
        Returns:
            observations: 新的观测
            rewards: 奖励
            terminations: 是否终止
            truncations: 是否截断
            infos: 额外信息
        """
        # 获取当前时间上下文
        time_context = self.time_manager.get_time_context(self.current_timeslot)
        
        # 1. 处理所有智能体的动作（任务分配决策）
        action_results = self._process_all_actions(actions)
        
        # 2. 执行一个时隙的计算（卫星和云）
        self._execute_computation_step(time_context)
        
        # 3. 更新物理状态（轨道、通信）
        self.current_timeslot += 1
        if self.current_timeslot < self.max_timeslots:
            next_time_context = self.time_manager.get_time_context(self.current_timeslot)
            # 使用真实的轨道数据更新物理状态
            self.current_visibility_matrices = self._get_real_visibility_matrices(self.current_timeslot)
            # 获取通信矩阵
            self.current_comm_matrix = {
                'isl': self.communication_manager.get_isl_communication_matrix(self.current_timeslot),
                'satellite_ground': self.communication_manager.get_satellite_ground_communication_matrix(self.current_timeslot),
                'satellite_cloud': self.communication_manager.get_satellite_cloud_communication_matrix(self.current_timeslot)
            }
            
            # 4. 生成新任务
            new_tasks_by_location = self.task_generator.generate_tasks_for_timeslot(self.current_timeslot)
            # 展平任务字典为任务列表
            all_new_tasks = []
            for location_id, tasks in new_tasks_by_location.items():
                all_new_tasks.extend(tasks)
            self._distribute_new_tasks(all_new_tasks)
        
        # 5. 计算奖励
        rewards = self._calculate_rewards(action_results)
        
        # 6. 检查终止条件
        done = self.current_timeslot >= self.max_timeslots
        terminations = {agent: done for agent in self.agents}
        truncations = {agent: False for agent in self.agents}
        
        # 7. 构建新观测
        if not done:
            observations = self._build_all_observations()
        else:
            # 终止时返回零观测
            observations = self._get_zero_observations()
        
        # 8. 收集信息
        infos = self._collect_step_info(action_results)
        
        return observations, rewards, terminations, truncations, infos
    
    def _distribute_initial_tasks(self, tasks: List[Task]):
        """分配初始任务到卫星队列"""
        if not tasks:
            return
        
        # 简单的轮询分配策略
        for i, task in enumerate(tasks):
            sat_idx = i % len(self.agents)
            agent_id = self.agents[sat_idx]
            
            if len(self.task_queues[agent_id]) < self.max_queue_size:
                self.task_queues[agent_id].append(task)
                self.total_tasks_generated += 1
    
    def _distribute_new_tasks(self, tasks: List[Task]):
        """分配新生成的任务 - 使用TaskDistributor最近距离原则"""
        if not tasks:
            return
        
        # 使用TaskDistributor进行基于距离的任务分配
        assignment_results = self.task_distributor.distribute_tasks(tasks, self.current_timeslot)
        
        for assignment in assignment_results:
            if assignment.status == AssignmentStatus.ASSIGNED:
                # 将任务分配给最近的卫星
                sat_id = assignment.assigned_satellite_id
                agent_id = f"sat_{sat_id}"
                
                # 验证智能体存在且队列未满
                if agent_id in self.agents and len(self.task_queues[agent_id]) < self.max_queue_size:
                    self.task_queues[agent_id].append(assignment.task)
                    self.total_tasks_generated += 1
                    
                    # 记录分配信息用于调试
                    logger.debug(f"Task {assignment.task_id} assigned to {agent_id} "
                               f"at distance {assignment.distance_km:.2f}km")
                else:
                    logger.warning(f"Failed to assign task {assignment.task_id} to {agent_id}: "
                                 f"agent_exists={agent_id in self.agents}, "
                                 f"queue_size={len(self.task_queues.get(agent_id, []))}")
            
            elif assignment.status == AssignmentStatus.RETRYING:
                # 任务进入重试队列，TaskDistributor会自动处理
                logger.debug(f"Task {assignment.task_id} entered retry queue")
                
            else:
                # 分配失败，记录日志
                logger.warning(f"Task {assignment.task_id} assignment failed: {assignment.status}")
                self.total_tasks_dropped += 1
    
    def _process_all_actions(self, actions: Dict[str, np.ndarray]) -> Dict:
        """处理所有智能体的动作序列"""
        # 准备可见性信息
        visibility_info = {
            'visible_neighbors': {},
            'ground_visible': {},
            'cloud_visible': {}
        }
        
        # 为每个智能体准备可见性信息
        for agent_id in self.agents:
            sat_idx = self._get_satellite_index(agent_id)
            visibility_info['visible_neighbors'][agent_id] = self._get_visible_neighbors(sat_idx)
            visibility_info['ground_visible'][agent_id] = self._check_ground_visibility(sat_idx)
            visibility_info['cloud_visible'][agent_id] = True  # 简化：假设总是可见
        
        # 使用动作处理器验证和处理动作
        validated_actions = self.action_handler.validate_batch_actions(
            actions, self.task_queues, visibility_info
        )
        
        # 执行验证后的动作
        results = self._execute_validated_actions(validated_actions)
        
        # 只移除已分配的任务，而不是清空整个队列
        for agent_id in self.agents:
            if agent_id in validated_actions:
                assigned_indices = set()
                for action in validated_actions[agent_id]:
                    if action['target'] != 'drop':
                        assigned_indices.add(action['task_index'])
                
                # 保留未分配的任务
                new_queue = []
                for i, task in enumerate(self.task_queues[agent_id]):
                    if i not in assigned_indices:
                        new_queue.append(task)
                self.task_queues[agent_id] = new_queue
        
        return results
    
    def _execute_validated_actions(self, validated_actions: Dict) -> Dict:
        """执行已验证的动作分配"""
        results = {}
        
        for agent_id, actions in validated_actions.items():
            agent_results = {
                'valid_actions': 0,
                'invalid_actions': 0,
                'tasks_assigned': 0,
                'local_assignments': 0,
                'neighbor_assignments': 0,
                'cloud_assignments': 0,
                'tasks_dropped': 0
            }
            
            for action in actions:
                task = action['task']
                target = action['target']
                
                if target.target_type == 'local':
                    # 本地处理
                    success = self.satellites[agent_id].add_task(task)
                    if success:
                        agent_results['valid_actions'] += 1
                        agent_results['local_assignments'] += 1
                        agent_results['tasks_assigned'] += 1
                    else:
                        agent_results['invalid_actions'] += 1
                        
                elif target.target_type == 'neighbor':
                    # 转发到邻居
                    target_agent_id = target.target_id
                    if target_agent_id and target_agent_id in self.satellites:
                        success = self.satellites[target_agent_id].add_task(task)
                        if success:
                            agent_results['valid_actions'] += 1
                            agent_results['neighbor_assignments'] += 1
                            agent_results['tasks_assigned'] += 1
                        else:
                            agent_results['invalid_actions'] += 1
                    else:
                        agent_results['invalid_actions'] += 1
                        
                elif target.target_type == 'cloud':
                    # 转发到云
                    cloud_idx = target.target_index
                    if cloud_idx is not None and cloud_idx < len(self.cloud_nodes):
                        cloud_id = f"cloud_{cloud_idx}"
                        self.cloud_nodes[cloud_id].add_task(task)
                        agent_results['valid_actions'] += 1
                        agent_results['cloud_assignments'] += 1
                        agent_results['tasks_assigned'] += 1
                    else:
                        agent_results['invalid_actions'] += 1
                        
                elif target == 'drop':
                    # 丢弃任务
                    agent_results['tasks_dropped'] += 1
                    self.total_tasks_dropped += 1
            
            results[agent_id] = agent_results
        
        return results
    
    def _execute_computation_step(self, time_context):
        """执行一个时隙的计算"""
        # 卫星计算
        for satellite in self.satellites.values():
            # 使用时隙持续时间，假设所有卫星都在光照下（简化）
            duration = self.timeslot_duration
            illuminated = True  # 简化：假设总是有光照
            satellite.process_timeslot(duration, illuminated)
        
        # 云计算
        for cloud in self.cloud_nodes.values():
            # CloudCompute 使用 process_batch 方法，传入时隙持续时间
            completed_tasks = cloud.process_batch(self.timeslot_duration)
            
            # 处理完成的任务结果返回
            if completed_tasks:
                self._process_cloud_task_results(cloud, completed_tasks)
    
    def _process_cloud_task_results(self, cloud, completed_tasks: List):
        """
        处理云计算完成的任务结果返回
        按要求：云服务器 → 最近卫星 → 原始地面站
        结果数据量为原始任务量的1/10
        """
        for task in completed_tasks:
            try:
                # 找到距离云中心最近的可见卫星
                nearest_sat_id, distance = self._find_nearest_satellite_to_cloud(
                    cloud.cloud_id, task.original_location_id
                )
                
                if nearest_sat_id is not None:
                    # 记录返回路径信息
                    task.return_satellite_id = nearest_sat_id
                    task.return_distance_km = distance
                    
                    # 计算结果返回的延迟和能耗（基于1/10数据量）
                    result_transmission_delay = self._calculate_result_return_delay(
                        task.result_data_size, distance
                    )
                    result_energy_cost = self._calculate_result_return_energy(
                        task.result_data_size, distance
                    )
                    
                    # 更新任务的总延迟和能耗
                    task.communication_delay += result_transmission_delay
                    task.total_delay = task.processing_delay + task.communication_delay
                    task.energy_consumed += result_energy_cost
                    
                    logger.debug(f"Task {task.task_id} result return: "
                               f"cloud -> sat_{nearest_sat_id} -> ground_{task.original_location_id}, "
                               f"result_size={task.result_data_size:.2f}MB, "
                               f"return_delay={result_transmission_delay:.3f}s")
                else:
                    logger.warning(f"No visible satellite found for returning task {task.task_id} result")
                    
            except Exception as e:
                logger.error(f"Failed to process result return for task {task.task_id}: {e}")
    
    def _find_nearest_satellite_to_cloud(self, cloud_id: str, original_location_id: int) -> Tuple[Optional[int], Optional[float]]:
        """
        找到距离云中心最近的可见卫星用于结果返回
        """
        try:
            # 获取当前云-卫星可见性矩阵
            cloud_comm = self.communication_manager.get_satellite_cloud_communication_matrix(self.current_timeslot)
            cloud_idx = int(cloud_id.split('_')[1])  # 从cloud_0提取索引
            
            # 找到可见的卫星
            visibility = cloud_comm['visibility']
            distances = cloud_comm['distance_km']
            
            # 获取云中心到所有卫星的可见性和距离
            visible_sats = np.where(visibility[:, cloud_idx])[0]  # 可见的卫星索引
            
            if len(visible_sats) > 0:
                # 选择距离最近的可见卫星
                visible_distances = distances[visible_sats, cloud_idx]
                min_dist_idx = np.argmin(visible_distances)
                nearest_sat_idx = visible_sats[min_dist_idx]
                min_distance = visible_distances[min_dist_idx]
                
                # 转换为卫星ID
                nearest_sat_id = 111 + nearest_sat_idx
                return nearest_sat_id, min_distance
            else:
                return None, None
                
        except Exception as e:
            logger.error(f"Error finding nearest satellite to cloud {cloud_id}: {e}")
            return None, None
    
    def _calculate_result_return_delay(self, result_data_size_mb: float, distance_km: float) -> float:
        """
        计算结果返回的传输延迟
        基于1/10数据量和通信参数
        """
        try:
            # 使用卫星-云下行链路参数计算
            comm_config = self.config['communication']
            bandwidth_hz = comm_config['b_sc_hz']  # 卫星到云的带宽
            
            # 数据量转换为比特
            data_bits = result_data_size_mb * 8 * 1024 * 1024
            
            # 传输延迟 = 数据量 / 带宽
            transmission_delay = data_bits / bandwidth_hz
            
            # 传播延迟
            propagation_delay = (distance_km * 1000) / comm_config['light_speed_ms'] / 1000
            
            total_delay = transmission_delay + propagation_delay
            return total_delay
            
        except Exception as e:
            logger.error(f"Error calculating result return delay: {e}")
            return 0.0
    
    def _calculate_result_return_energy(self, result_data_size_mb: float, distance_km: float) -> float:
        """
        计算结果返回的能耗
        基于1/10数据量和通信功率
        """
        try:
            comm_config = self.config['communication']
            # 使用卫星到云的发射功率
            tx_power_w = comm_config['p_sc_w']
            
            # 传输时间
            data_bits = result_data_size_mb * 8 * 1024 * 1024
            bandwidth_hz = comm_config['b_sc_hz']
            transmission_time = data_bits / bandwidth_hz
            
            # 能耗 = 功率 × 时间
            energy_joules = tx_power_w * transmission_time
            return energy_joules
            
        except Exception as e:
            logger.error(f"Error calculating result return energy: {e}")
            return 0.0

    def _calculate_rewards(self, action_results: Dict) -> Dict[str, float]:
        """使用RewardCalculator组件计算奖励"""
        # 收集卫星状态
        satellite_states = {}
        for agent_id in self.agents:
            queue_status = self.satellites[agent_id].get_queue_status()
            energy_status = self.satellites[agent_id].get_energy_status()
            statistics = self.satellites[agent_id].get_statistics()
            
            satellite_states[agent_id] = {
                'completed_tasks': statistics.get('total_completed', 0),
                'dropped_tasks': statistics.get('total_dropped', 0),
                'energy_consumed': energy_status.get('total_consumed', 0),
                'cpu_usage': statistics.get('avg_utilization', 0),
                'memory_usage': statistics.get('avg_utilization', 0),  # 使用相同的利用率指标
                'queue_length': queue_status.get('queue_length', 0)
            }
        
        # 收集任务统计（RewardCalculator期望的格式）
        # 注：_update_episode_stats方法期望task_statistics.values()是字典
        # 这里简化处理，将统计信息包装成单个字典
        task_statistics = {
            'global': {  # 使用'global'作为键，包含全局统计
                'total_generated': self.total_tasks_generated,
                'total_completed': self.total_tasks_completed,
                'total_dropped': self.total_tasks_dropped,
                'average_delay': 0,  # 需要实际计算
                'timeslot': self.current_timeslot
            }
        }
        
        # 使用RewardCalculator计算奖励
        rewards = self.reward_calculator.calculate_rewards(
            action_results=action_results,
            satellite_states=satellite_states,
            task_statistics=task_statistics
        )
        
        # 更新累积奖励
        for agent_id in self.agents:
            self.cumulative_rewards[agent_id] += rewards.get(agent_id, 0.0)
        
        return rewards
    
    def _build_all_observations(self) -> Dict:
        """构建所有智能体的观测"""
        observations = {}
        for agent_id in self.agents:
            observations[agent_id] = self.observation_builder.build_observation(
                agent_id=agent_id,
                satellite=self.satellites[agent_id],
                task_queue=self.task_queues[agent_id],
                visibility_matrices=self.current_visibility_matrices,
                comm_matrix=self.current_comm_matrix,
                current_timeslot=self.current_timeslot,
                max_timeslots=self.max_timeslots,
                satellite_index=self._get_satellite_index(agent_id),
                all_satellites=self.satellites
            )
        
        return observations
    
    def _get_zero_observations(self) -> Dict:
        """获取零观测（用于终止状态）"""
        zero_obs = {}
        for agent in self.agents:
            zero_obs[agent] = {
                'own_state': np.zeros(self.state_feature_dim, dtype=np.float32),
                'task_queue': np.zeros((self.max_queue_size, self.task_feature_dim), dtype=np.float32),
                'task_mask': np.zeros(self.max_queue_size, dtype=np.int8),
                'action_mask': np.zeros((self.max_queue_size, self.num_action_targets), dtype=np.int8),
                'neighbor_states': np.zeros((self.max_visible_neighbors, self.neighbor_feature_dim), dtype=np.float32),
                'comm_quality': np.zeros(self.max_visible_neighbors, dtype=np.float32),
                'time_info': np.zeros(3, dtype=np.float32)
            }
        return zero_obs
    
    def _collect_step_info(self, action_results: Dict) -> Dict:
        """收集步骤信息"""
        infos = {}
        
        # 获取TaskDistributor统计信息
        distributor_stats = self.task_distributor.get_statistics()
        
        for agent_id in self.agents:
            queue_status = self.satellites[agent_id].get_queue_status()
            energy_status = self.satellites[agent_id].get_energy_status()
            statistics = self.satellites[agent_id].get_statistics()
            
            info = {
                'tasks_in_queue': len(self.task_queues[agent_id]),
                'tasks_completed': statistics.get('total_completed', 0),
                'tasks_dropped': statistics.get('total_dropped', 0),
                'energy_consumed': energy_status.get('total_consumed', 0),
                'cpu_usage': statistics.get('avg_utilization', 0),
                'cumulative_reward': self.cumulative_rewards[agent_id],
                # 添加任务分配统计信息
                'task_assignment_success_rate': distributor_stats.get('assignment_success_rate', 0),
                'avg_assignment_distance': distributor_stats.get('avg_assignment_distance', 0)
            }
            
            if agent_id in action_results:
                info.update(action_results[agent_id])
            
            infos[agent_id] = info
        
        return infos
    
    def _get_satellite_index(self, agent_id: str) -> int:
        """获取卫星索引"""
        # 从agent_id提取卫星编号
        # agent_id格式: "sat_111", "sat_112", ...
        sat_num = int(agent_id.split('_')[1])
        return sat_num - 111  # 转换为0-based索引
    
    def _get_visible_neighbors(self, sat_idx: int) -> List[int]:
        """获取可见的邻居卫星索引"""
        if self.current_visibility_matrices is None:
            return []
        
        sat_to_sat = self.current_visibility_matrices.get('satellite_to_satellite', None)
        if sat_to_sat is None:
            return []
        
        # 获取可见的卫星
        visible = np.where(sat_to_sat[sat_idx] > 0)[0]
        return visible.tolist()
    
    def _check_ground_visibility(self, sat_idx: int) -> bool:
        """检查是否有地面站可见"""
        if self.current_visibility_matrices is None:
            return False
        
        sat_to_ground = self.current_visibility_matrices.get('satellite_to_ground', None)
        if sat_to_ground is None:
            return False
        
        return np.any(sat_to_ground[sat_idx] > 0)
    
    # def _convert_to_compute_task(self, task: Task) -> ComputeTask:
    #     """将Task转换为ComputeTask - 已移除，转换逻辑统一到计算节点内部"""
    #     return ComputeTask(
    #         task_id=task.task_id,
    #         priority=float(task.priority),
    #         deadline=task.deadline_timestamp,
    #         data_size_mb=task.data_size_mb,
    #         complexity=task.complexity_cycles_per_bit * task.data_size_mb * 8 * 1e6,
    #         drop_penalty=50.0,
    #         arrival_time=task.generation_time
    #     )
    
    def _get_real_visibility_matrices(self, timeslot: int) -> Dict:
        """
        获取真实的可见性矩阵（基于轨道动力学计算）
        
        Args:
            timeslot: 当前时隙
            
        Returns:
            包含三种可见性矩阵的字典
        """
        try:
            # 使用轨道模块计算真实的可见性矩阵
            time_context = self.time_manager.get_time_context(timeslot)
            
            # 获取卫星位置
            satellites_at_time = self.orbital_updater.get_satellites_at_time(timeslot)
            
            # 构建可见性矩阵
            # 使用实际存在的方法名
            sat_to_sat_visibility, sat_to_sat_distance = self.orbital_updater.build_visibility_matrix(satellites_at_time)
            sat_to_ground_visibility, sat_to_ground_distance = self.orbital_updater.build_satellite_ground_visibility_matrix(satellites_at_time, timeslot)
            sat_to_cloud_visibility, sat_to_cloud_distance = self.orbital_updater.build_satellite_cloud_visibility_matrix(satellites_at_time, timeslot)
            
            visibility_matrices = {
                'satellite_to_satellite': sat_to_sat_visibility,
                'satellite_to_ground': sat_to_ground_visibility,
                'satellite_to_cloud': sat_to_cloud_visibility,
                'distances': {
                    'satellite_to_satellite': sat_to_sat_distance,
                    'satellite_to_ground': sat_to_ground_distance,
                    'satellite_to_cloud': sat_to_cloud_distance
                }
            }
            
            return visibility_matrices
            
        except Exception as e:
            logger.error(f"Failed to get real visibility matrices for timeslot {timeslot}: {str(e)}")
            # 如果获取真实数据失败，使用轨道模块的默认方法
            return self._get_fallback_visibility_matrices(timeslot)
    
    def _get_fallback_visibility_matrices(self, timeslot: int) -> Dict:
        """
        备用可见性矩阵获取方法（当轨道数据不可用时）
        
        Args:
            timeslot: 当前时隙
            
        Returns:
            基于配置的可见性矩阵
        """
        logger.warning(f"Using fallback visibility matrices for timeslot {timeslot}")
        
        # 使用通信管理器的基础可见性计算
        try:
            # 生成简化的可见性矩阵
            num_satellites = self.num_satellites
            num_ground = 420
            num_cloud = 5
            
            # 简化假设：所有卫星都可以互相通信
            sat_to_sat = np.ones((num_satellites, num_satellites)) - np.eye(num_satellites)
            
            # 简化假设：每个卫星可以看到附近的地面站
            sat_to_ground = np.random.rand(num_satellites, num_ground) > 0.8
            
            # 简化假设：所有卫星都可以访问云
            sat_to_cloud = np.ones((num_satellites, num_cloud))
            
            # 生成随机距离矩阵
            sat_to_sat_dist = np.random.uniform(500, 2000, (num_satellites, num_satellites))
            sat_to_ground_dist = np.random.uniform(500, 1500, (num_satellites, num_ground))
            sat_to_cloud_dist = np.random.uniform(800, 1500, (num_satellites, num_cloud))
            
            return {
                'satellite_to_satellite': sat_to_sat,
                'satellite_to_ground': sat_to_ground,
                'satellite_to_cloud': sat_to_cloud,
                'distances': {
                    'satellite_to_satellite': sat_to_sat_dist,
                    'satellite_to_ground': sat_to_ground_dist,
                    'satellite_to_cloud': sat_to_cloud_dist
                }
            }
        except Exception as e:
            logger.error(f"Fallback visibility calculation failed: {str(e)}")
            raise RuntimeError(f"Unable to obtain visibility matrices for timeslot {timeslot}. "
                             "Please check orbital data and communication manager configuration.")
    
    def render(self):
        """渲染环境状态"""
        if self.render_mode == "human":
            print(f"\n{'='*60}")
            print(f"Timeslot: {self.current_timeslot}/{self.max_timeslots}")
            print(f"Total Tasks: Generated={self.total_tasks_generated}, "
                  f"Completed={self.total_tasks_completed}, Dropped={self.total_tasks_dropped}")
            print(f"{'='*60}")
            
            # 显示前5个卫星的状态
            for i, agent_id in enumerate(self.agents[:5]):
                statistics = self.satellites[agent_id].get_statistics()
                print(f"{agent_id}: Queue={len(self.task_queues[agent_id])}, "
                      f"Completed={statistics.get('total_completed', 0)}, "
                      f"Dropped={statistics.get('total_dropped', 0)}, "
                      f"Reward={self.cumulative_rewards[agent_id]:.2f}")
    
    def close(self):
        """清理资源"""
        pass