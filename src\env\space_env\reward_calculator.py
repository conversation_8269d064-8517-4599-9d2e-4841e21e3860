"""
奖励计算器
基于任务完成情况、延迟、能耗等多维度指标计算奖励
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class RewardComponents:
    """奖励组成部分"""
    task_completion: float = 0.0
    task_drop_penalty: float = 0.0
    delay_penalty: float = 0.0
    energy_penalty: float = 0.0
    load_balance_bonus: float = 0.0
    invalid_action_penalty: float = 0.0
    cooperation_bonus: float = 0.0
    total: float = 0.0


class RewardCalculator:
    """
    奖励计算器
    计算多维度的奖励信号，引导Transformer-MAPPO学习最优策略
    """
    
    def __init__(self, config: Dict):
        """
        初始化奖励计算器
        
        Args:
            config: 奖励配置
        """
        self.config = config
        
        # 奖励权重
        self.weights = config.get('reward_weights', {
            'completion': 10.0,
            'drop': -5.0,
            'delay': -0.01,
            'energy': -0.001,
            'load_balance': 1.0,
            'invalid_action': -1.0,
            'cooperation': 2.0
        })
        
        # 归一化参数
        self.normalization = config.get('normalization', {
            'max_tasks_per_step': 100,
            'max_delay': 1000.0,  # seconds
            'max_energy': 10000.0,  # Joules
            'target_load_variance': 0.1
        })
        
        # 历史统计
        self.episode_stats = self._init_episode_stats()
    
    def _init_episode_stats(self) -> Dict:
        """初始化episode统计"""
        return {
            'total_tasks_completed': 0,
            'total_tasks_dropped': 0,
            'total_energy_consumed': 0.0,
            'average_delay': 0.0,
            'step_count': 0
        }
    
    def reset(self):
        """重置奖励计算器"""
        self.episode_stats = self._init_episode_stats()
    
    def calculate_rewards(self,
                         action_results: Dict,
                         satellite_states: Dict,
                         task_statistics: Dict,
                         global_metrics: Optional[Dict] = None) -> Dict[str, float]:
        """
        计算所有智能体的奖励
        
        Args:
            action_results: 动作执行结果
            satellite_states: 卫星状态
            task_statistics: 任务统计
            global_metrics: 全局指标（可选）
        
        Returns:
            奖励字典 {agent_id: reward}
        """
        rewards = {}
        reward_details = {}
        
        # 计算全局负载均衡指标
        load_variance = self._calculate_load_variance(satellite_states)
        
        for agent_id, state in satellite_states.items():
            # 计算各个奖励组件
            components = RewardComponents()
            
            # 1. 任务完成奖励
            components.task_completion = self._calculate_completion_reward(
                state, task_statistics.get(agent_id, {})
            )
            
            # 2. 任务丢弃惩罚
            components.task_drop_penalty = self._calculate_drop_penalty(
                state, task_statistics.get(agent_id, {})
            )
            
            # 3. 延迟惩罚
            components.delay_penalty = self._calculate_delay_penalty(
                state, task_statistics.get(agent_id, {})
            )
            
            # 4. 能耗惩罚
            components.energy_penalty = self._calculate_energy_penalty(state)
            
            # 5. 负载均衡奖励
            components.load_balance_bonus = self._calculate_load_balance_bonus(
                state, load_variance
            )
            
            # 6. 无效动作惩罚
            if agent_id in action_results:
                components.invalid_action_penalty = self._calculate_invalid_action_penalty(
                    action_results[agent_id]
                )
            
            # 7. 协作奖励
            components.cooperation_bonus = self._calculate_cooperation_bonus(
                agent_id, action_results, satellite_states
            )
            
            # 计算总奖励
            components.total = (
                components.task_completion +
                components.task_drop_penalty +
                components.delay_penalty +
                components.energy_penalty +
                components.load_balance_bonus +
                components.invalid_action_penalty +
                components.cooperation_bonus
            )
            
            rewards[agent_id] = components.total
            reward_details[agent_id] = components
        
        # 更新统计
        self._update_episode_stats(satellite_states, task_statistics)
        
        return rewards
    
    def _calculate_completion_reward(self, state: Dict, task_stats: Dict) -> float:
        """计算任务完成奖励"""
        completed = state.get('completed_tasks', 0)
        
        # 基础完成奖励
        base_reward = completed * self.weights['completion']
        
        # 考虑任务优先级加成
        if 'completed_priority_sum' in task_stats:
            priority_bonus = task_stats['completed_priority_sum'] * 0.5
            base_reward += priority_bonus
        
        # 归一化
        normalized_reward = base_reward / self.normalization['max_tasks_per_step']
        
        return normalized_reward
    
    def _calculate_drop_penalty(self, state: Dict, task_stats: Dict) -> float:
        """计算任务丢弃惩罚"""
        dropped = state.get('dropped_tasks', 0)
        
        # 基础丢弃惩罚
        base_penalty = dropped * self.weights['drop']
        
        # 考虑丢弃任务的优先级
        if 'dropped_priority_sum' in task_stats:
            priority_penalty = task_stats['dropped_priority_sum'] * 1.0
            base_penalty -= priority_penalty  # 注意这里是减法，增加惩罚
        
        return base_penalty
    
    def _calculate_delay_penalty(self, state: Dict, task_stats: Dict) -> float:
        """计算延迟惩罚"""
        avg_delay = task_stats.get('average_delay', 0.0)
        
        if avg_delay <= 0:
            return 0.0
        
        # 非线性延迟惩罚（延迟越大惩罚增长越快）
        normalized_delay = min(avg_delay / self.normalization['max_delay'], 1.0)
        penalty = self.weights['delay'] * (normalized_delay ** 2) * 100
        
        # 考虑超时任务
        timeout_count = task_stats.get('timeout_count', 0)
        if timeout_count > 0:
            penalty += timeout_count * self.weights['drop'] * 0.5
        
        return penalty
    
    def _calculate_energy_penalty(self, state: Dict) -> float:
        """计算能耗惩罚"""
        energy_consumed = state.get('energy_consumed', 0.0)
        
        # 归一化能耗
        normalized_energy = min(energy_consumed / self.normalization['max_energy'], 1.0)
        
        # 基础能耗惩罚
        penalty = self.weights['energy'] * normalized_energy * 100
        
        # 电池电量奖励/惩罚
        battery_level = state.get('battery_level', 50.0)
        if battery_level < 20:
            # 低电量额外惩罚
            penalty -= (20 - battery_level) * 0.1
        elif battery_level > 80:
            # 高电量小奖励
            penalty += (battery_level - 80) * 0.01
        
        return penalty
    
    def _calculate_load_balance_bonus(self, state: Dict, load_variance: float) -> float:
        """计算负载均衡奖励"""
        # 如果负载方差低于目标值，给予奖励
        target_variance = self.normalization['target_load_variance']
        
        if load_variance < target_variance:
            bonus = self.weights['load_balance'] * (1.0 - load_variance / target_variance)
        else:
            # 负载不均衡，给予惩罚
            bonus = -self.weights['load_balance'] * (load_variance - target_variance) * 2
        
        # 个体负载考虑
        queue_length = state.get('queue_length', 0)
        cpu_usage = state.get('cpu_usage', 0)
        
        # 如果个体负载适中，额外奖励
        if 20 < queue_length < 80 and 30 < cpu_usage < 70:
            bonus += self.weights['load_balance'] * 0.5
        
        return bonus
    
    def _calculate_invalid_action_penalty(self, action_result: Dict) -> float:
        """计算无效动作惩罚"""
        invalid_actions = action_result.get('invalid_actions', 0)
        
        # 每个无效动作的惩罚
        penalty = invalid_actions * self.weights['invalid_action']
        
        # 如果无效动作比例过高，额外惩罚
        total_actions = action_result.get('valid_actions', 0) + invalid_actions
        if total_actions > 0:
            invalid_ratio = invalid_actions / total_actions
            if invalid_ratio > 0.3:
                penalty -= self.weights['invalid_action'] * invalid_ratio * 5
        
        return penalty
    
    def _calculate_cooperation_bonus(self,
                                    agent_id: str,
                                    action_results: Dict,
                                    satellite_states: Dict) -> float:
        """计算协作奖励"""
        bonus = 0.0
        
        if agent_id not in action_results:
            return bonus
        
        agent_result = action_results[agent_id]
        
        # 成功转发任务给邻居的奖励
        neighbor_assignments = agent_result.get('neighbor_assignments', 0)
        if neighbor_assignments > 0:
            bonus += neighbor_assignments * self.weights['cooperation'] * 0.5
        
        # 成功转发到云的奖励
        cloud_assignments = agent_result.get('cloud_assignments', 0)
        if cloud_assignments > 0:
            bonus += cloud_assignments * self.weights['cooperation'] * 0.3
        
        # 如果帮助了过载的邻居，额外奖励
        # 这需要更复杂的逻辑来判断，这里简化处理
        local_queue = satellite_states[agent_id].get('queue_length', 0)
        if local_queue < 5 and neighbor_assignments > 0:
            # 自己负载低但帮助了其他卫星
            bonus += self.weights['cooperation']
        
        return bonus
    
    def _calculate_load_variance(self, satellite_states: Dict) -> float:
        """计算负载方差"""
        loads = []
        for state in satellite_states.values():
            # 综合考虑队列长度和CPU使用率
            queue_length = state.get('queue_length', 0)
            cpu_usage = state.get('cpu_usage', 0)
            combined_load = (queue_length / 100.0 + cpu_usage / 100.0) / 2.0
            loads.append(combined_load)
        
        if len(loads) > 0:
            return np.var(loads)
        return 0.0
    
    def _update_episode_stats(self, satellite_states: Dict, task_statistics: Dict):
        """更新episode统计"""
        self.episode_stats['step_count'] += 1
        
        for state in satellite_states.values():
            self.episode_stats['total_tasks_completed'] += state.get('completed_tasks', 0)
            self.episode_stats['total_tasks_dropped'] += state.get('dropped_tasks', 0)
            self.episode_stats['total_energy_consumed'] += state.get('energy_consumed', 0)
        
        # 更新平均延迟
        total_delay = sum(stats.get('average_delay', 0) for stats in task_statistics.values())
        if len(task_statistics) > 0:
            avg_delay = total_delay / len(task_statistics)
            alpha = 0.1  # 指数移动平均系数
            self.episode_stats['average_delay'] = (
                alpha * avg_delay + (1 - alpha) * self.episode_stats['average_delay']
            )
    
    def get_episode_summary(self) -> Dict:
        """获取episode总结"""
        if self.episode_stats['step_count'] == 0:
            return {}
        
        total_tasks = (self.episode_stats['total_tasks_completed'] + 
                      self.episode_stats['total_tasks_dropped'])
        
        return {
            'completion_rate': self.episode_stats['total_tasks_completed'] / max(total_tasks, 1),
            'average_delay': self.episode_stats['average_delay'],
            'total_energy': self.episode_stats['total_energy_consumed'],
            'energy_per_task': self.episode_stats['total_energy_consumed'] / max(
                self.episode_stats['total_tasks_completed'], 1
            ),
            'total_completed': self.episode_stats['total_tasks_completed'],
            'total_dropped': self.episode_stats['total_tasks_dropped']
        }
    
    def compute_shaped_reward(self,
                             current_state: Dict,
                             previous_state: Dict,
                             agent_id: str) -> float:
        """
        计算塑形奖励（reward shaping）
        基于状态改善给予额外奖励
        
        Args:
            current_state: 当前状态
            previous_state: 上一步状态
            agent_id: 智能体ID
        
        Returns:
            塑形奖励
        """
        shaped_reward = 0.0
        
        # 队列长度改善
        prev_queue = previous_state.get('queue_length', 0)
        curr_queue = current_state.get('queue_length', 0)
        if curr_queue < prev_queue:
            shaped_reward += (prev_queue - curr_queue) * 0.1
        
        # 电池电量管理
        prev_battery = previous_state.get('battery_level', 50)
        curr_battery = current_state.get('battery_level', 50)
        
        # 充电奖励
        if curr_battery > prev_battery and curr_battery < 90:
            shaped_reward += (curr_battery - prev_battery) * 0.01
        
        # 避免过放电
        if prev_battery < 30 and curr_battery > prev_battery:
            shaped_reward += 0.5
        
        return shaped_reward