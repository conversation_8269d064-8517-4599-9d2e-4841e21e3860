#!/usr/bin/env python3
"""
测试MAPPO环境中的任务分配是否使用最近距离原则
验证修改后的task distribution逻辑
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.env.space_env import make_env
from src.env.physics_layer.task_models import Task, TaskType

def test_task_distribution():
    """测试任务分配是否使用最近距离原则"""
    print("="*60)
    print("测试MAPPO环境任务分配 - 最近距离原则验证")
    print("="*60)
    
    try:
        # 创建环境
        print("1. 初始化环境...")
        env = make_env()
        
        # 重置环境
        print("2. 重置环境...")
        observations, infos = env.reset()
        print(f"   环境重置成功，智能体数量: {len(observations)}")
        
        # 检查TaskDistributor是否正确初始化
        print("3. 验证TaskDistributor集成...")
        if hasattr(env, 'task_distributor'):
            print("   TaskDistributor successfully integrated")
            
            # 获取TaskDistributor统计信息
            distributor_stats = env.task_distributor.get_statistics()
            print(f"   - 初始统计: {distributor_stats}")
        else:
            print("   TaskDistributor not properly integrated")
            return False
        
        # 模拟运行几个时隙
        print("4. 运行环境步骤...")
        for step in range(1, 4):
            print(f"\n   时隙 {step}:")
            
            # 生成随机动作
            actions = {}
            for agent_id in observations.keys():
                # 生成序列动作：每个任务选择本地执行(0)
                actions[agent_id] = np.zeros(env.max_queue_size, dtype=int)
            
            # 执行步骤
            observations, rewards, terminations, truncations, infos = env.step(actions)
            
            # 检查任务分配信息
            for agent_id, info in infos.items():
                if 'task_assignment_success_rate' in info:
                    print(f"     {agent_id}: 任务队列={info['tasks_in_queue']}, "
                          f"分配成功率={info['task_assignment_success_rate']:.2f}, "
                          f"平均分配距离={info.get('avg_assignment_distance', 0):.2f}km")
                    break  # 只打印第一个智能体的信息作为示例
            
            # 检查是否有任务分配
            total_tasks = sum(info['tasks_in_queue'] for info in infos.values())
            print(f"     总任务数: {total_tasks}")
            
            if all(terminations.values()):
                break
        
        # 最终验证
        print("\n5. 最终验证...")
        final_stats = env.task_distributor.get_statistics()
        print(f"   最终TaskDistributor统计: {final_stats}")
        
        # 检查关键指标
        success_rate = final_stats.get('assignment_success_rate', 0)
        avg_distance = final_stats.get('avg_assignment_distance', 0)
        
        print(f"\n6. 验证结果:")
        print(f"   - 任务分配成功率: {success_rate:.2f}")
        print(f"   - 平均分配距离: {avg_distance:.2f}km")
        
        # 判断是否使用了最近距离原则
        if success_rate > 0 and avg_distance > 0:
            print("   Success: Environment is using TaskDistributor for nearest distance assignment")
            return True
        else:
            print("   Warning: Potential issues detected, need to check task generation and distribution logic")
            return False
            
    except Exception as e:
        print(f"   Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("\n" + "="*60)

def test_distance_comparison():
    """对比修改前后的分配策略"""
    print("\n测试距离对比:")
    print("-" * 40)
    
    try:
        env = make_env()
        observations, _ = env.reset()
        
        # 模拟创建任务来测试分配逻辑
        test_task = Task(
            task_id=999,
            task_type=TaskType.COMPUTE,
            location_id=1,  # 地面站ID
            cpu_demand=1.0,
            memory_demand=1.0,
            data_size=10.0,
            deadline_timestamp=100.0,
            priority=1,
            created_timestamp=0.0
        )
        
        # 使用TaskDistributor分配
        assignment = env.task_distributor.assign_task_to_satellite(test_task, 1)
        
        if assignment.satellite_id is not None:
            print(f"TaskDistributor分配结果:")
            print(f"  - 选中卫星: sat_{assignment.satellite_id}")
            print(f"  - 分配距离: {assignment.distance_km:.2f}km")
            print(f"  - 分配状态: {assignment.status}")
            print("  Used nearest distance principle")
        else:
            print("  TaskDistributor assignment failed")
            
    except Exception as e:
        print(f"  Distance comparison test failed: {e}")

if __name__ == "__main__":
    # 运行测试
    success = test_task_distribution()
    test_distance_comparison()
    
    if success:
        print("\nAll tests passed! MAPPO environment successfully integrated nearest distance task assignment principle")
    else:
        print("\nTests failed, need further code inspection")