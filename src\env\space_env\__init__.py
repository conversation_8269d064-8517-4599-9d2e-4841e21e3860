"""
SPACE3 PettingZoo Environment for Transformer-MAPPO
"""

from .space3_pettingzoo_env import Space3PettingZooEnv
from .sequence_action_handler import SequenceActionHandler, ActionTarget
from .observation_builder import ObservationBuilder, ObservationConfig
from .reward_calculator import RewardCalculator, RewardComponents

__version__ = "1.0.0"
__author__ = "SPACE3 Team"

__all__ = [
    # 主环境类
    "Space3PettingZooEnv",
    
    # 动作处理
    "SequenceActionHandler",
    "ActionTarget",
    
    # 观测构建
    "ObservationBuilder", 
    "ObservationConfig",
    
    # 奖励计算
    "RewardCalculator",
    "RewardComponents",
    
    # 便捷函数
    "make_env",
    "make_parallel_env"
]


def make_env(config_path: str = None, **kwargs):
    """
    创建单个环境实例
    
    Args:
        config_path: 配置文件路径
        **kwargs: 额外参数
    
    Returns:
        Space3PettingZooEnv实例
    """
    return Space3PettingZooEnv(config_path=config_path)


def make_parallel_env(n_envs: int = 1, config_path: str = None, **kwargs):
    """
    创建多个并行环境（用于并行训练）
    
    Args:
        n_envs: 环境数量
        config_path: 配置文件路径
        **kwargs: 额外参数
    
    Returns:
        环境列表
    """
    envs = []
    for i in range(n_envs):
        env = Space3PettingZooEnv(config_path=config_path)
        envs.append(env)
    return envs