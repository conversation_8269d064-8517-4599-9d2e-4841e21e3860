"""
Multi-Agent Replay Buffer for MAPPO
多智能体经验缓冲区 - 模仿参考代码的ReplayBuffer结构
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Generator

from src.agent.mappo_trans.config import MAPPOConfig


class MultiAgentBuffer:
    """
    多智能体经验缓冲区
    存储所有智能体的经验，支持GAE计算和批次采样
    """
    
    def __init__(self, config: MAPPOConfig):
        self.config = config
        self.buffer_size = config.rollout_length  # 等于rollout长度
        self.n_agents = config.n_agents
        self.obs_dim = config.obs_dim
        self.action_dim = config.sequence_length  # 动作序列长度
        
        # 为每个智能体分配存储空间
        self.reset()
        
    def reset(self):
        """重置缓冲区"""
        # 每个智能体的经验数据
        self.observations = {}
        self.actions = {}
        self.log_probs = {}
        self.rewards = {}
        self.values = {}
        self.dones = {}
        
        # GAE相关
        self.advantages = {}
        self.returns = {}
        
        # 初始化每个智能体的存储
        for agent_id in range(self.n_agents):
            agent_key = f"sat_{111 + agent_id}"  # 与环境一致的命名
            self._init_agent_storage(agent_key)
            
        self.ptr = 0  # 当前指针
        self.full = False  # 缓冲区是否已满
        
    def _init_agent_storage(self, agent_id: str):
        """初始化单个智能体的存储空间"""
        self.observations[agent_id] = np.zeros((self.buffer_size, self.obs_dim), dtype=np.float32)
        self.actions[agent_id] = np.zeros((self.buffer_size, self.action_dim), dtype=np.int32)
        self.log_probs[agent_id] = np.zeros((self.buffer_size, self.action_dim), dtype=np.float32)
        self.rewards[agent_id] = np.zeros((self.buffer_size,), dtype=np.float32)
        self.values[agent_id] = np.zeros((self.buffer_size,), dtype=np.float32)
        self.dones[agent_id] = np.zeros((self.buffer_size,), dtype=np.bool_)
        
        # GAE结果
        self.advantages[agent_id] = np.zeros((self.buffer_size,), dtype=np.float32)
        self.returns[agent_id] = np.zeros((self.buffer_size,), dtype=np.float32)
    
    def store(self, 
              observations: Dict[str, np.ndarray],
              actions: Dict[str, np.ndarray], 
              log_probs: Dict[str, np.ndarray],
              rewards: Dict[str, float],
              values: Dict[str, float],
              dones: Dict[str, bool]):
        """
        存储一步的经验
        
        Args:
            observations: {agent_id: obs_vector}
            actions: {agent_id: action_sequence}
            log_probs: {agent_id: log_prob_sequence}
            rewards: {agent_id: reward_scalar}
            values: {agent_id: value_scalar}
            dones: {agent_id: done_bool}
        """
        for agent_id in observations.keys():
            if agent_id not in self.observations:
                continue
                
            self.observations[agent_id][self.ptr] = observations[agent_id]
            self.actions[agent_id][self.ptr] = actions[agent_id]
            self.log_probs[agent_id][self.ptr] = log_probs[agent_id]
            self.rewards[agent_id][self.ptr] = rewards[agent_id]
            self.values[agent_id][self.ptr] = values[agent_id]
            self.dones[agent_id][self.ptr] = dones[agent_id]
        
        self.ptr = (self.ptr + 1) % self.buffer_size
        if self.ptr == 0:
            self.full = True
    
    def compute_returns_and_advantages(self, gamma: float = 0.99, gae_lambda: float = 0.95):
        """
        计算GAE优势和回报
        模仿参考代码的GAE计算方式
        """
        for agent_id in self.observations.keys():
            rewards = self.rewards[agent_id][:self.ptr if not self.full else self.buffer_size]
            values = self.values[agent_id][:self.ptr if not self.full else self.buffer_size]
            dones = self.dones[agent_id][:self.ptr if not self.full else self.buffer_size]
            
            # GAE计算
            advantages = np.zeros_like(rewards)
            last_gae_lam = 0
            
            for step in reversed(range(len(rewards))):
                if step == len(rewards) - 1:
                    next_non_terminal = 1.0 - dones[step]
                    next_values = 0  # 假设episode结束时价值为0
                else:
                    next_non_terminal = 1.0 - dones[step]
                    next_values = values[step + 1]
                
                delta = rewards[step] + gamma * next_values * next_non_terminal - values[step]
                advantages[step] = last_gae_lam = delta + gamma * gae_lambda * next_non_terminal * last_gae_lam
            
            # 计算回报
            returns = advantages + values
            
            # 存储结果
            length = len(advantages)
            self.advantages[agent_id][:length] = advantages
            self.returns[agent_id][:length] = returns
    
    def get_all_data(self) -> Dict[str, torch.Tensor]:
        """
        获取所有数据，转换为torch.Tensor
        用于训练时的数据获取
        """
        length = self.ptr if not self.full else self.buffer_size
        
        # 将所有智能体的数据concatenate
        all_obs = []
        all_actions = []
        all_log_probs = []
        all_advantages = []
        all_returns = []
        all_values = []
        
        for agent_id in self.observations.keys():
            all_obs.append(self.observations[agent_id][:length])
            all_actions.append(self.actions[agent_id][:length])
            all_log_probs.append(self.log_probs[agent_id][:length])
            all_advantages.append(self.advantages[agent_id][:length])
            all_returns.append(self.returns[agent_id][:length])
            all_values.append(self.values[agent_id][:length])
        
        # 转换为tensor
        data = {
            'observations': torch.FloatTensor(np.concatenate(all_obs, axis=0)),
            'actions': torch.LongTensor(np.concatenate(all_actions, axis=0)),
            'log_probs': torch.FloatTensor(np.concatenate(all_log_probs, axis=0)),
            'advantages': torch.FloatTensor(np.concatenate(all_advantages, axis=0)),
            'returns': torch.FloatTensor(np.concatenate(all_returns, axis=0)),
            'values': torch.FloatTensor(np.concatenate(all_values, axis=0))
        }
        
        return data
    
    def get_batches(self, batch_size: int) -> Generator[Dict[str, torch.Tensor], None, None]:
        """
        生成训练批次
        模仿参考代码的batch采样方式
        
        Args:
            batch_size: 批次大小
            
        Yields:
            批次数据字典
        """
        data = self.get_all_data()
        total_size = data['observations'].shape[0]
        
        # 随机打乱索引
        indices = torch.randperm(total_size)
        
        # 生成batch
        for start in range(0, total_size, batch_size):
            end = min(start + batch_size, total_size)
            batch_indices = indices[start:end]
            
            batch = {
                'observations': data['observations'][batch_indices],
                'actions': data['actions'][batch_indices],
                'log_probs': data['log_probs'][batch_indices],
                'advantages': data['advantages'][batch_indices],
                'returns': data['returns'][batch_indices],
                'values': data['values'][batch_indices]
            }
            
            yield batch
    
    def get_statistics(self) -> Dict[str, float]:
        """获取缓冲区统计信息"""
        if self.ptr == 0 and not self.full:
            return {'size': 0, 'avg_reward': 0.0, 'avg_value': 0.0}
        
        length = self.ptr if not self.full else self.buffer_size
        
        # 计算所有智能体的平均统计
        total_rewards = []
        total_values = []
        
        for agent_id in self.observations.keys():
            total_rewards.extend(self.rewards[agent_id][:length])
            total_values.extend(self.values[agent_id][:length])
        
        stats = {
            'size': length * self.n_agents,
            'avg_reward': np.mean(total_rewards) if total_rewards else 0.0,
            'avg_value': np.mean(total_values) if total_values else 0.0,
            'std_reward': np.std(total_rewards) if total_rewards else 0.0,
            'max_reward': np.max(total_rewards) if total_rewards else 0.0,
            'min_reward': np.min(total_rewards) if total_rewards else 0.0
        }
        
        return stats
    
    def clear(self):
        """清空缓冲区"""
        self.reset()
    
    def is_ready(self, min_size: int = None) -> bool:
        """检查缓冲区是否准备好训练"""
        if min_size is None:
            min_size = self.config.batch_size
        
        current_size = self.ptr if not self.full else self.buffer_size
        return current_size * self.n_agents >= min_size


# 兼容参考代码的简化Buffer类
class ReplayBuffer(MultiAgentBuffer):
    """
    为了兼容参考代码结构的简化Buffer
    实际上继承MultiAgentBuffer的功能
    """
    
    def __init__(self, config: MAPPOConfig):
        super().__init__(config)
        
        # 兼容参考代码的单智能体接口
        self.count = 0
        
        # 单智能体存储（为了兼容）
        self.s = np.zeros((config.rollout_length, config.obs_dim))
        self.a = np.zeros((config.rollout_length, config.sequence_length))
        self.a_logprob = np.zeros((config.rollout_length, config.sequence_length))
        self.r = np.zeros((config.rollout_length,))
        self.s_ = np.zeros((config.rollout_length, config.obs_dim))
        self.dw = np.zeros((config.rollout_length,))
        self.done = np.zeros((config.rollout_length,))
    
    def store_single(self, s, a, a_logprob, r, s_, dw, done):
        """兼容参考代码的单智能体存储接口"""
        self.s[self.count] = s
        self.a[self.count] = a
        self.a_logprob[self.count] = a_logprob
        self.r[self.count] = r
        self.s_[self.count] = s_
        self.dw[self.count] = dw
        self.done[self.count] = done
        self.count += 1
    
    def numpy_to_tensor(self):
        """兼容参考代码的tensor转换接口"""
        s = torch.tensor(self.s[:self.count], dtype=torch.float)
        a = torch.tensor(self.a[:self.count], dtype=torch.long)
        a_logprob = torch.tensor(self.a_logprob[:self.count], dtype=torch.float)
        r = torch.tensor(self.r[:self.count], dtype=torch.float)
        s_ = torch.tensor(self.s_[:self.count], dtype=torch.float)
        dw = torch.tensor(self.dw[:self.count], dtype=torch.float)
        done = torch.tensor(self.done[:self.count], dtype=torch.float)
        
        return s, a, a_logprob, r, s_, dw, done


if __name__ == "__main__":
    # 测试缓冲区
    from src.agent.mappo_trans.config import get_default_config
    
    config = get_default_config()
    buffer = MultiAgentBuffer(config)
    
    print(f"Buffer initialized for {config.n_agents} agents")
    print(f"Buffer size: {buffer.buffer_size}")
    print(f"Observation dim: {buffer.obs_dim}")
    print(f"Action dim: {buffer.action_dim}")
    
    # 模拟存储一些数据
    for step in range(10):
        obs = {f"sat_{111+i}": np.random.randn(config.obs_dim) for i in range(config.n_agents)}
        actions = {f"sat_{111+i}": np.random.randint(0, 16, config.sequence_length) for i in range(config.n_agents)}
        log_probs = {f"sat_{111+i}": np.random.randn(config.sequence_length) for i in range(config.n_agents)}
        rewards = {f"sat_{111+i}": np.random.randn() for i in range(config.n_agents)}
        values = {f"sat_{111+i}": np.random.randn() for i in range(config.n_agents)}
        dones = {f"sat_{111+i}": False for i in range(config.n_agents)}
        
        buffer.store(obs, actions, log_probs, rewards, values, dones)
    
    # 计算GAE
    buffer.compute_returns_and_advantages()
    
    # 获取统计信息
    stats = buffer.get_statistics()
    print(f"Buffer stats: {stats}")
    
    # 测试批次生成
    for i, batch in enumerate(buffer.get_batches(batch_size=64)):
        print(f"Batch {i}: {batch['observations'].shape}")
        if i >= 2:  # 只测试前几个batch
            break