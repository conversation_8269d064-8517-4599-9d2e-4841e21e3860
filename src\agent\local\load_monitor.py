"""
Satellite Load Monitor for SPACE3 Local Computation
Generates dynamic GIF visualization of satellite load distribution over time
"""

import sys
import os
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from matplotlib.colors import LinearSegmentedColormap
from typing import Dict, List, Tuple, Any
import logging

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Absolute imports
from src.agent.local.local_compute_simulator import LocalComputeSimulator
from src.env.Foundation_Layer.logging_config import get_logger


class SatelliteLoadMonitor:
    """
    Monitor and visualize satellite load distribution over time
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize load monitor
        
        Args:
            config_path: Path to configuration file
        """
        self.logger = get_logger(__name__)
        
        # Load configuration and create simulator
        self.simulator = LocalComputeSimulator(config_path=config_path)
        
        # Load monitoring parameters
        self.w_queue = 0.6  # Queue load weight
        self.w_cpu = 0.4    # CPU utilization weight
        self.sampling_interval = 10  # Every 10 timeslots
        
        # Data storage
        self.load_data = {}  # timeslot -> {sat_id: load_value}
        self.position_data = {}  # timeslot -> {sat_id: (lat, lon)}
        
        # Visualization parameters
        self.fig_width = 15
        self.fig_height = 8
        self.dpi = 100
        
        self.logger.info("SatelliteLoadMonitor initialized")
    
    def calculate_composite_load(self, satellite_stats: Dict[str, Any]) -> float:
        """
        Calculate composite load indicator
        
        Args:
            satellite_stats: Satellite statistics dictionary
            
        Returns:
            Composite load value [0, 1]
        """
        # Extract load metrics
        queue_length = satellite_stats.get('queue_length', 0)
        cpu_utilization = satellite_stats.get('cpu_utilization', 0.0)
        
        # Get maximum queue size from config
        max_queue_size = self.simulator.config['queuing']['max_queue_size']
        
        # Normalize queue load
        normalized_queue = min(queue_length / max_queue_size, 1.0)
        
        # CPU utilization is already normalized [0, 1]
        normalized_cpu = min(cpu_utilization, 1.0)
        
        # Calculate composite load
        composite_load = self.w_queue * normalized_queue + self.w_cpu * normalized_cpu
        
        return min(composite_load, 1.0)
    
    def collect_load_data(self, total_timeslots: int = 1000) -> None:
        """
        Run simulation and collect load data for specified timeslots
        
        Args:
            total_timeslots: Total number of timeslots to simulate
        """
        self.logger.info(f"Starting controlled simulation for {total_timeslots} timeslots...")
        
        # Force override the simulator's config to ensure correct timeslot count
        original_timeslots = self.simulator.total_timeslots
        self.simulator.total_timeslots = total_timeslots
        self.simulator.config['system']['total_timeslots'] = total_timeslots
        
        try:
            # Initialize simulation components manually
            self.simulator._initialize_components()
            
            # Reset statistics
            self.simulator.timeslot_results = []
            self.simulator.all_completed_tasks = []
            self.simulator.all_dropped_tasks = []
            self.simulator.all_tasks_map = {}
            
            self.logger.info("Running step-by-step simulation...")
            
            # Run simulation step by step with data collection
            for timeslot in range(total_timeslots):
                if timeslot % 100 == 0:
                    self.logger.info(f"Processing timeslot {timeslot}/{total_timeslots}")
                
                # Process single timeslot
                result = self.simulator._process_single_timeslot(timeslot)
                self.simulator.timeslot_results.append(result)
                
                # Collect data every sampling_interval timeslots
                if timeslot % self.sampling_interval == 0 or timeslot == total_timeslots - 1:
                    self.logger.debug(f"Collecting data for timeslot {timeslot}")
                    
                    # Get satellite positions (use 0-based indexing for orbital data)
                    satellites = self.simulator.orbital_updater.get_satellites_at_time(timeslot)
                    
                    if not satellites:
                        self.logger.warning(f"No satellite data for timeslot {timeslot}")
                        continue
                    
                    # Store position data
                    positions = {}
                    loads = {}
                    
                    for sat_id_str, satellite in satellites.items():
                        sat_id = int(sat_id_str)
                        
                        # Store position
                        positions[sat_id] = (satellite.latitude, satellite.longitude)
                        
                        # Get real-time satellite load statistics
                        if sat_id in self.simulator.satellites:
                            sat_stats = self.simulator.satellites[sat_id].get_queue_status()
                            composite_load = self.calculate_composite_load(sat_stats)
                            loads[sat_id] = composite_load
                        else:
                            loads[sat_id] = 0.0
                    
                    # Store data (use 1-based indexing for visualization)
                    self.position_data[timeslot + 1] = positions
                    self.load_data[timeslot + 1] = loads
            
            self.logger.info(f"Simulation completed successfully after {total_timeslots} timeslots")
            self.logger.info(f"Load data collected for {len(self.load_data)} sampling points")
            
        except Exception as e:
            self.logger.error(f"Simulation failed: {e}")
            raise
        finally:
            # Restore original config
            self.simulator.total_timeslots = original_timeslots
            self.simulator.config['system']['total_timeslots'] = original_timeslots
    
    def create_load_visualization_frame(self, timeslot: int, ax) -> None:
        """
        Create a single frame of load visualization
        
        Args:
            timeslot: Current timeslot
            ax: Matplotlib axis to draw on
        """
        # Clear previous frame
        ax.clear()
        
        # Set up map projection
        ax.set_global()
        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.5)
        ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.5)
        
        # Add gridlines
        ax.gridlines(draw_labels=True, linewidth=0.3, alpha=0.7)
        
        # Get data for this timeslot
        if timeslot not in self.position_data or timeslot not in self.load_data:
            ax.set_title(f"Timeslot {timeslot}: No Data Available", fontsize=14, fontweight='bold')
            return
        
        positions = self.position_data[timeslot]
        loads = self.load_data[timeslot]
        
        # Prepare data for plotting
        lats = []
        lons = []
        load_values = []
        
        for sat_id in positions:
            lat, lon = positions[sat_id]
            load = loads.get(sat_id, 0.0)
            
            lats.append(lat)
            lons.append(lon)
            load_values.append(load)
        
        if not lats:
            ax.set_title(f"Timeslot {timeslot}: No Satellite Data", fontsize=14, fontweight='bold')
            return
        
        # Create scatter plot with load-based coloring
        scatter = ax.scatter(lons, lats, c=load_values, cmap='RdYlGn_r', 
                           s=80, alpha=0.8, edgecolors='black', linewidth=0.5,
                           vmin=0, vmax=1, transform=ccrs.PlateCarree())
        
        # Add title
        avg_load = np.mean(load_values) if load_values else 0
        ax.set_title(f"Satellite Load Distribution - Timeslot {timeslot}\n"
                    f"Average Load: {avg_load:.3f} | Active Satellites: {len(lats)}", 
                    fontsize=12, fontweight='bold', pad=20)
        
        # Add colorbar (only once, will be reused)
        if not hasattr(self, 'colorbar_added'):
            cbar = plt.colorbar(scatter, ax=ax, orientation='horizontal', 
                              pad=0.05, shrink=0.8, aspect=30)
            cbar.set_label('Composite Load (Queue 60% + CPU 40%)', fontsize=10)
            cbar.ax.tick_params(labelsize=9)
            self.colorbar_added = True
    
    def generate_load_gif(self, output_path: str = "satellite_load_animation_1-1000.gif", 
                         fps: int = 10) -> None:
        """
        Generate GIF animation of satellite load over time
        
        Args:
            output_path: Output file path for GIF
            fps: Frames per second for GIF
        """
        if not self.load_data:
            raise ValueError("No load data available. Run collect_load_data() first.")
        
        self.logger.info("Creating GIF animation...")
        
        # Set up the figure
        fig = plt.figure(figsize=(self.fig_width, self.fig_height), dpi=self.dpi)
        ax = fig.add_subplot(1, 1, 1, projection=ccrs.PlateCarree())
        
        # Reset colorbar flag
        self.colorbar_added = False
        
        # Get sorted timeslots
        timeslots = sorted(self.load_data.keys())
        
        def animate(frame_idx):
            """Animation function"""
            timeslot = timeslots[frame_idx]
            self.create_load_visualization_frame(timeslot, ax)
            return ax.collections
        
        # Create animation
        anim = animation.FuncAnimation(fig, animate, frames=len(timeslots), 
                                     interval=1000//fps, blit=False, repeat=False)
        
        # Save as GIF
        self.logger.info(f"Saving GIF animation to {output_path}...")
        try:
            anim.save(output_path, writer='pillow', fps=fps, 
                     savefig_kwargs={'bbox_inches': 'tight', 'pad_inches': 0.1})
            self.logger.info(f"GIF animation saved successfully: {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save GIF: {e}")
            raise
        finally:
            plt.close(fig)
    
    def generate_load_statistics(self) -> Dict[str, Any]:
        """
        Generate load statistics summary
        
        Returns:
            Dictionary containing load statistics
        """
        if not self.load_data:
            return {}
        
        all_loads = []
        timeslot_avg_loads = {}
        
        for timeslot, loads in self.load_data.items():
            load_values = list(loads.values())
            all_loads.extend(load_values)
            timeslot_avg_loads[timeslot] = np.mean(load_values) if load_values else 0
        
        stats = {
            'total_timeslots': len(self.load_data),
            'total_satellites': len(next(iter(self.load_data.values()), {})),
            'overall_mean_load': np.mean(all_loads) if all_loads else 0,
            'overall_max_load': np.max(all_loads) if all_loads else 0,
            'overall_min_load': np.min(all_loads) if all_loads else 0,
            'overall_std_load': np.std(all_loads) if all_loads else 0,
            'peak_load_timeslot': max(timeslot_avg_loads.items(), 
                                    key=lambda x: x[1], default=(None, 0))[0],
            'min_load_timeslot': min(timeslot_avg_loads.items(), 
                                   key=lambda x: x[1], default=(None, 0))[0]
        }
        
        return stats
    
    def run_monitoring(self, total_timeslots: int = 1000, 
                      output_path: str = "satellite_load_animation_1-1000.gif",
                      fps: int = 10) -> Dict[str, Any]:
        """
        Run complete monitoring process
        
        Args:
            total_timeslots: Total number of timeslots to simulate
            output_path: Output GIF file path
            fps: GIF frame rate
            
        Returns:
            Load statistics dictionary
        """
        try:
            # Step 1: Collect load data
            self.collect_load_data(total_timeslots)
            
            # Step 2: Generate GIF
            self.generate_load_gif(output_path, fps)
            
            # Step 3: Generate statistics
            stats = self.generate_load_statistics()
            
            # Print summary
            self.logger.info("Monitoring completed successfully!")
            self.logger.info(f"GIF saved: {output_path}")
            self.logger.info(f"Statistics: Mean Load = {stats.get('overall_mean_load', 0):.3f}")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Monitoring failed: {e}")
            raise


def main():
    """Main function for standalone execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate satellite load monitoring GIF')
    parser.add_argument('--timeslots', type=int, default=1000,
                       help='Number of timeslots to simulate (default: 1000)')
    parser.add_argument('--output', type=str, default='satellite_load_animation_1-1000.gif',
                       help='Output GIF file path')
    parser.add_argument('--fps', type=int, default=10,
                       help='GIF frame rate (default: 10)')
    parser.add_argument('--config', type=str, default=None,
                       help='Configuration file path')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("="*60)
    print(" SPACE3 Satellite Load Monitor")
    print("="*60)
    print(f"Timeslots: {args.timeslots}")
    print(f"Output: {args.output}")
    print(f"FPS: {args.fps}")
    print("="*60)
    
    try:
        # Create monitor
        monitor = SatelliteLoadMonitor(config_path=args.config)
        
        # Run monitoring
        stats = monitor.run_monitoring(args.timeslots, args.output, args.fps)
        
        # Print final statistics
        print("\nLoad Statistics:")
        print(f"  Total Timeslots: {stats.get('total_timeslots', 0)}")
        print(f"  Active Satellites: {stats.get('total_satellites', 0)}")
        print(f"  Mean Load: {stats.get('overall_mean_load', 0):.3f}")
        print(f"  Max Load: {stats.get('overall_max_load', 0):.3f}")
        print(f"  Load Std Dev: {stats.get('overall_std_load', 0):.3f}")
        print(f"  Peak Load Timeslot: {stats.get('peak_load_timeslot', 'N/A')}")
        
        print("\n" + "="*60)
        print(" Monitoring Complete!")
        print("="*60)
        
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()